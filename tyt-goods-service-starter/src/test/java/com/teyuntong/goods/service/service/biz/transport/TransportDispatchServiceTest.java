package com.teyuntong.goods.service.service.biz.transport;

import cn.hutool.core.lang.Assert;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.transport.dto.TransportDispatchDTO;
import com.teyuntong.goods.service.client.transport.service.TransportDispatchRpcService;
import com.teyuntong.goods.service.client.transport.vo.TransportDispatchVO;
import com.teyuntong.infra.common.rocketmq.core.BatchConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.ConsumerBeanInitializer;
import com.teyuntong.infra.common.rocketmq.core.OrderedConsumerBeanInitializer;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;


/**
 * 代调服务测试类
 *
 * <AUTHOR>
 * @since 2024-01-18 16:07
 */
@Slf4j
@Disabled("老旧单元测试")
class TransportDispatchServiceTest extends TestBase {

    /**
     * 使用 @MockBean 防止 spring boot 创建 ConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private ConsumerBeanInitializer consumerBeanInitializer;
    /**
     * 使用 @MockBean 防止 spring boot 创建 orderedConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private OrderedConsumerBeanInitializer orderedConsumerBeanInitializer;
    /**
     * 使用 @MockBean 防止 spring boot 创建 batchConsumerBeanInitializer, 导致测试容器类消费消息
     */
    @MockBean
    private BatchConsumerBeanInitializer batchConsumerBeanInitializer;

    @Autowired
    private TransportDispatchRpcService transportDispatchRpcService;

    /**
     * 查询货源代调信息
     */
    @Test
    void query() {
        TransportDispatchDTO transportDispatchDTO = new TransportDispatchDTO();
//        transportDispatchDTO.setSrcMsgId(33831435L);
        TransportDispatchVO transportDispatchVO = transportDispatchRpcService.query(transportDispatchDTO);
        System.out.println(transportDispatchVO);
    }


}

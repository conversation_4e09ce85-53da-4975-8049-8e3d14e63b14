package com.teyuntong.goods.service.service.biz.goodsname;

import cn.hutool.json.JSONUtil;
import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.goodsname.dto.GoodsMatchRpcDto;
import com.teyuntong.goods.service.client.goodsname.service.GoodsMatchRpcService;
import com.teyuntong.goods.service.client.goodsname.vo.GoodsMatchVo;
import com.teyuntong.goods.service.service.biz.goodsname.service.GoodsMatchService;
import com.teyuntong.goods.service.service.common.utils.IKAnalyzerUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import java.io.StringReader;
import java.util.List;
import java.util.Set;


/**
 * 客户资料服务测试类
 *
 * <AUTHOR>
 * @since 2024-01-18 16:07
 */
@Slf4j
@Disabled("老旧单元测试")
class GoodsMatchRpcServiceImplTest extends TestBase {

    @Autowired
    private GoodsMatchRpcService goodsMatchRpcService;
    @Autowired
    private GoodsMatchService goodsMatchService;
    @Autowired
    private IKAnalyzerUtils ikAnalyzerUtils;


    @Test
    void searchGoodsMatch() {
        GoodsMatchRpcDto goodsMatchRpcDto = new GoodsMatchRpcDto();
        goodsMatchRpcDto.setKeywords("挖装机");
        goodsMatchRpcDto.setStandardName("挖掘机,打桩机");
        goodsMatchRpcDto.setPageNum(3);
        GoodsMatchVo goodsMatchVo = goodsMatchRpcService.searchGoodsMatch(goodsMatchRpcDto);
        System.out.println(JSONUtil.toJsonStr(goodsMatchVo));
    }

    @Test
    void analysisKeywordsTest() {
        String text = "三一350强夯机";
        List<String> analyze = ikAnalyzerUtils.analyze(text);
        System.out.println(JSONUtil.toJsonStr(analyze));
    }
}

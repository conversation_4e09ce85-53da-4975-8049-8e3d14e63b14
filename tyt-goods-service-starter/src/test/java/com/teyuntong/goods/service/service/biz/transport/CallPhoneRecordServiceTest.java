package com.teyuntong.goods.service.service.biz.transport;

import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.callphonerecord.service.CallPhoneRecordRpcService;
import com.teyuntong.goods.service.client.callphonerecord.vo.GetCarPhoneVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

/**
 * 货源发布
 *
 * <AUTHOR>
 * @since 2025-04-07 15:52
 */
@Slf4j
@Disabled("老旧单元测试")
public class CallPhoneRecordServiceTest extends TestBase {
    @Resource
    private CallPhoneRecordRpcService callPhoneRecordRpcService;

    @Test
    void showExcellentPricePopup() {
        GetCarPhoneVo phone = callPhoneRecordRpcService.getPhone(1002001217L);
        System.out.println(phone);
    }

}

package com.teyuntong.goods.service.service.biz.transport;

import com.teyuntong.goods.service.TestBase;
import com.teyuntong.goods.service.client.invoice.vo.HbwjInvoiceTransportOverrunCheckReq;
import com.teyuntong.goods.service.client.publish.dto.UpgradeExcellentDTO;
import com.teyuntong.goods.service.client.publish.service.TransportPublishRpcService;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.GoodsContactTimeDTO;
import com.teyuntong.goods.service.service.biz.invoice.service.InvoiceEnterpriseService;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.common.utils.TimeUtil;
import com.teyuntong.goods.service.service.rpc.publish.TransportPublishRpcServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 货源发布
 *
 * <AUTHOR>
 * @since 2025-04-07 15:52
 */
@Slf4j
@Disabled("老旧单元测试")
public class TransportPublishServiceTest extends TestBase {
    @Resource
    private TransportPublishRpcServiceImpl transportPublishRpcService;

    @Resource
    private TransportAfterOrderDataService transportAfterOrderDataService;

    @Autowired
    private InvoiceEnterpriseService invoiceEnterpriseService;

    @Test
    void showExcellentPricePopup() {
        UpgradeExcellentDTO upgradeExcellentDTO = transportPublishRpcService.showExcellentPricePopup(88823519L);
        log.info("{}", upgradeExcellentDTO);
    }

    @Test
    void calculateMedianMinutes() {
        List<GoodsContactTimeDTO> list = new ArrayList<>();
        GoodsContactTimeDTO dto = new GoodsContactTimeDTO();
        dto.setSrcMsgId(1L);
        dto.setContactMinutes(42);
        list.add(dto);
        GoodsContactTimeDTO dto2 = new GoodsContactTimeDTO();
        dto2.setSrcMsgId(2L);
        dto2.setContactMinutes(1);
        list.add(dto2);
        GoodsContactTimeDTO dto3 = new GoodsContactTimeDTO();
        dto3.setSrcMsgId(3L);
        dto3.setContactMinutes(7);
        list.add(dto3);
        GoodsContactTimeDTO dto4 = new GoodsContactTimeDTO();
        dto4.setSrcMsgId(4L);
        dto4.setContactMinutes(7);
        list.add(dto4);
        Integer integer = transportAfterOrderDataService.calculateMedianMinutes(list, GoodsContactTimeDTO::getContactMinutes);
        log.info("{}", integer);
    }

    public static void main(String[] args) {
        Date startTime = TimeUtil.addDay(-30);
        System.out.println(startTime);
    }

    @Test
    void test43242() {
        HbwjInvoiceTransportOverrunCheckReq hbwjInvoiceTransportOverrunCheckReq = new HbwjInvoiceTransportOverrunCheckReq();
        hbwjInvoiceTransportOverrunCheckReq.setDistance(new BigDecimal("1000"));
        hbwjInvoiceTransportOverrunCheckReq.setWeight(new BigDecimal("10"));
        hbwjInvoiceTransportOverrunCheckReq.setPrice(new BigDecimal("10000"));
        Boolean b = invoiceEnterpriseService.hbwjInvoiceTransportOverrunCheck(hbwjInvoiceTransportOverrunCheckReq);
        System.out.println(1);
    }
}

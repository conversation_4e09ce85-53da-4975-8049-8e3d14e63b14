<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.TransportExposureMapper">

    <select id="getBySrcMsgId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportExposureDO">
        select *
        from tyt_transport_exposure
        where src_msg_id = #{srcMsgId}
    </select>

    <select id="getMaxChangeId" resultType="java.lang.Long">
        select max(change_id) from tyt_transport_exposure
    </select>

    <select id="getCountByUserIdAndTimeScope" resultType="java.lang.Integer">
        select count(*)
        from tyt_transport_main tt inner join tyt_transport_exposure tte on tt.src_msg_id = tte.src_msg_id
        where tt.user_id = #{userId}
        and tte.create_time &gt; #{todayString} and tte.create_time &lt; CONCAT(#{todayString},' 23:59:59')
        <if test="status != null">
            and tt.status = #{status}
        </if>
    </select>
</mapper>

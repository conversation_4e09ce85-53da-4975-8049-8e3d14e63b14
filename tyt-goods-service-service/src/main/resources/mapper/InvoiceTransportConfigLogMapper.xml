<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.invoice.mybatis.mapper.InvoiceTransportConfigLogMapper">

    <select id="getLastConfig" resultType="com.teyuntong.goods.service.service.biz.invoice.mybatis.entity.InvoiceTransportConfigLogDO">
        select *
        from tyt_invoice_transport_config_log
        order by id desc
        limit 1
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.UserCallPhoneRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.UserCallPhoneRecordDO">
        <id column="id" property="id" />
        <result column="ts_id" property="tsId" />
        <result column="user_id" property="userId" />
        <result column="ctime" property="ctime" />
        <result column="level" property="level" />
        <result column="path" property="path" />
        <result column="plat_id" property="platId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, ts_id, user_id, ctime, level, path, plat_id
    </sql>

    <select id="getUserCallPhoneCount" resultType="java.lang.Integer">
        select count(*) from tyt_user_call_phone_record
        where user_id = #{userId}
        <if test="startTime != null and endTime != null">
            and ctime >= #{startTime} and ctime &lt;= #{endTime}
        </if>
    </select>

	<select id="getCallStatusByUserIdAndTsId" resultType="java.lang.Integer">
        select COUNT(*)
        from tyt_user_call_phone_record tucpr
        where tucpr.`ts_id`= #{srcMsgId} and tucpr.`user_id`= #{userId} and tucpr.ctime > #{startTime}
    </select>

</mapper>

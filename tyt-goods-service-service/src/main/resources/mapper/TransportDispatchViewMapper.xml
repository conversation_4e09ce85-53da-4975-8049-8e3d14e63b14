<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.mapper.TransportDispatchViewMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO">
        <id column="id" property="id"/>
        <result column="src_msg_id" property="srcMsgId"/>
        <result column="car_user_id" property="carUserId"/>
        <result column="car_user_name" property="carUserName"/>
        <result column="car_nick_name" property="carNickName"/>
        <result column="car_phone" property="carPhone"/>
        <result column="remark" property="remark"/>
        <result column="remark_user_id" property="remarkUserId"/>
        <result column="remark_user_name" property="remarkUserName"/>
        <result column="remark_time" property="remarkTime"/>
        <result column="view_count" property="viewCount"/>
        <result column="contact_count" property="contactCount"/>
        <result column="view_time" property="viewTime"/>
        <result column="contact_time" property="contactTime"/>
        <result column="create_time" property="createTime"/>
        <result column="modify_time" property="modifyTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, src_msg_id, car_user_id, car_user_name, car_nick_name, car_phone, remark, remark_user_id, remark_user_name,
        remark_time, view_count, contact_count, view_time, contact_time, create_time, modify_time
    </sql>

    <select id="selectViewAndContactCount" resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO">
        SELECT src_msg_id as srcMsgId,
               COUNT(IF(contact_count > 0, 1, NULL)) contactCount,
               COUNT(IF(view_count > 0, 1, NULL)) viewCount
        FROM tyt_transport_dispatch_view
        WHERE create_time > CURRENT_DATE() and src_msg_id in
        <foreach collection="srcMsgIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        group by src_msg_id
    </select>

    <!-- 统计某个货源的联系次数和查看次数，一个用户拨打多次算一次 -->
    <select id="countContactAndViewBySrcMsgId" resultMap="BaseResultMap">
        SELECT
            COUNT(IF(contact_count > 0, 1, NULL)) contact_count,
            COUNT(IF(view_count > 0, 1, NULL)) view_count
        FROM tyt_transport_dispatch_view
        WHERE src_msg_id= #{srcMsgId}
    </select>

    <select id="hasContactInSrcMsgIds" resultType="integer">
        SELECT 1
        FROM tyt_transport_dispatch_view
        WHERE src_msg_id IN ( <foreach collection="srcMsgIds" item="i" separator=","> #{i} </foreach> )
        AND contact_count > 0 limit 1
    </select>

    <select id="selectBySrcMsgIdAndCarUserId"
            resultType="com.teyuntong.goods.service.service.biz.callphonerecord.mybatis.entity.TransportDispatchViewDO">
        select *
        from tyt_transport_dispatch_view
        where src_msg_id = #{srcMsgId} and car_user_id = #{userId}
    </select>

</mapper>

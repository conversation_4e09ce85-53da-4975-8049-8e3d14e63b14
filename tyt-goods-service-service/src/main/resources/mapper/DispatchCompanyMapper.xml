<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.DispatchCompanyMapper">


    <select id="selectByUserId"
            resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCompanyDO">
        select *
        from tyt_dispatch_company
        where user_id = #{userId}
          and is_valid = 1
        limit 1
    </select>

    <select id="countByUserId" resultType="java.lang.Integer">
        select count(*)
        from tyt_dispatch_company
        where user_id = #{userId} and is_valid = 1
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.teyuntong.goods.service.service.biz.transport.mybatis.mapper.IntelligentDepositConfigMapper">


	<select id="getConfigByWeightAndDistance"
	        resultType="com.teyuntong.goods.service.service.biz.transport.mybatis.entity.IntelligentDepositConfigDO">
		select *
		from tyt_intelligent_deposit_config
		where start_tonnage &lt; #{weight} and end_tonnage &gt;= #{weight}
		  and start_distance &lt; #{distance} and end_distance &gt;= #{distance}
	</select>
</mapper>

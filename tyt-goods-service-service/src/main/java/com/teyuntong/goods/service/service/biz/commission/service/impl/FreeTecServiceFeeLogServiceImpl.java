package com.teyuntong.goods.service.service.biz.commission.service.impl;

import com.teyuntong.goods.service.service.biz.commission.mybatis.entity.FreeTecServiceFeeLogDO;
import com.teyuntong.goods.service.service.biz.commission.mybatis.mapper.FreeTecServiceFeeLogMapper;
import com.teyuntong.goods.service.service.biz.commission.service.FreeTecServiceFeeLogService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 货源免佣标记表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-19
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class FreeTecServiceFeeLogServiceImpl implements FreeTecServiceFeeLogService {

    private final FreeTecServiceFeeLogMapper freeTecServiceFeeLogMapper;

    /**
     * 按srcMsgId删除
     */
    @Override
    public void deleteBySrcMsgId(Long srcMsgId) {
        freeTecServiceFeeLogMapper.deleteBySrcMsgId(srcMsgId);
    }

    /**
     * 保存
     *
     * @param freeTecServiceFeeLogDO
     */
    @Override
    public void save(FreeTecServiceFeeLogDO freeTecServiceFeeLogDO) {
        freeTecServiceFeeLogMapper.insert(freeTecServiceFeeLogDO);
    }
}

package com.teyuntong.goods.service.service.biz.transport.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-03-27
 */
@Getter
@Setter
@TableName("tyt_transport_collect")
public class TransportCollectDO {

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 电话
     */
    private String cellPhone;

    /**
     * 信息id
     */
    private Long infoId;

    /**
     * 终端标识
     */
    private Boolean platId;

    /**
     * 采集时间
     */
    private Date ctime;

    /**
     * 状态
     */
    private Boolean status;

    /**
     * 车ID
     */
    private Long carId;

    /**
     * 车头牌照头字母
     */
    private String headCity;

    /**
     * 车头牌照号码
     */
    private String headNo;

    /**
     * 挂车牌照头字母
     */
    private String tailCity;

    /**
     * 挂车牌照号码
     */
    private String tailNo;

    /**
     * 用户ID
     */
    private Long userId;
}

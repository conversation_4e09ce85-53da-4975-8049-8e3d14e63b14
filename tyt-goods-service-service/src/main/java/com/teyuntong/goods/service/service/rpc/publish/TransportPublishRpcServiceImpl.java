package com.teyuntong.goods.service.service.rpc.publish;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.teyuntong.goods.service.client.publish.dto.*;
import com.teyuntong.goods.service.client.publish.service.TransportPublishRpcService;
import com.teyuntong.goods.service.client.transport.dto.*;
import com.teyuntong.goods.service.client.transport.vo.CarryPriceVO;
import com.teyuntong.goods.service.client.transport.vo.TransportCarryReq;
import com.teyuntong.goods.service.client.transport.vo.TransportPublishVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.commission.bo.TecServiceFeeConfigComputeResult;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsConfigService;
import com.teyuntong.goods.service.service.biz.invoice.dto.InvoiceTransportTaxInfoDTO;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceQueryDTO;
import com.teyuntong.goods.service.service.biz.order.dto.SameTransportAvgPriceResultDTO;
import com.teyuntong.goods.service.service.biz.order.mybatis.entity.TransportAfterOrderDataDO;
import com.teyuntong.goods.service.service.biz.order.service.TransportAfterOrderDataService;
import com.teyuntong.goods.service.service.biz.publish.service.TransportAutoResendService;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPopupTrackingLogService;
import com.teyuntong.goods.service.service.biz.publish.service.TransportPublishService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.*;
import com.teyuntong.goods.service.service.biz.transport.service.*;
import com.teyuntong.goods.service.service.common.enums.*;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.common.properties.PromptProperties;
import com.teyuntong.goods.service.service.common.utils.IdCardUtil;
import com.teyuntong.goods.service.service.common.utils.TransportUtil;
import com.teyuntong.goods.service.service.common.utils.TytStrUtil;
import com.teyuntong.goods.service.service.remote.basic.ABTestRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytCityRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.basic.TytSequenceRemoteService;
import com.teyuntong.goods.service.service.remote.bi.BiGoodModelResult;
import com.teyuntong.goods.service.service.remote.user.ExcellentGoodsCardRemoteService;
import com.teyuntong.goods.service.service.remote.user.ThirdEnterpriseRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.goods.service.service.rpc.bi.BiRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.PublishProcessBO;
import com.teyuntong.goods.service.service.rpc.publish.builder.*;
import com.teyuntong.goods.service.service.rpc.publish.checker.*;
import com.teyuntong.goods.service.service.rpc.publish.commission.CalcCommissionTecFeeService;
import com.teyuntong.goods.service.service.rpc.publish.converter.TransportPublishConverter;
import com.teyuntong.goods.service.service.rpc.publish.enums.PublishOptEnum;
import com.teyuntong.goods.service.service.rpc.publish.post.*;
import com.teyuntong.infra.basic.resource.client.tytcity.vo.TytCityVo;
import com.teyuntong.infra.common.definition.bean.BaseParamDTO;
import com.teyuntong.infra.common.definition.bean.LoginUserDTO;
import com.teyuntong.infra.common.definition.bean.WebResult;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.infra.common.web.resolve.LoginHelper;
import com.teyuntong.user.service.client.permission.dto.GainExcellentGoodsCardRpcDTO;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import com.teyuntong.user.service.client.user.vo.UserRpcVO;
import com.teyuntong.user.service.client.user.vo.UserSubRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.text.StrPool.COMMA;
import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.SHOW_BOSS_NICK_NAME;
import static com.teyuntong.goods.service.service.common.constant.AbtestKeyConstant.YOUCHE_IS_SHOW_USERNAMEABTEST_CODE;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.GOODS_DETAIL_REMARK_PROMPT;
import static com.teyuntong.goods.service.service.common.constant.ConfigKeyConstant.INVOICE_SUBJECT_DATA;
import static com.teyuntong.goods.service.service.common.enums.PublishStyleEnum.HISTORY_PUBLISH;
import static com.teyuntong.goods.service.service.common.enums.PublishStyleEnum.TODAY_PUBLISH;

/**
 * 货源发布接口
 *
 * <AUTHOR>
 * @since 2024/12/12 16:18
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class TransportPublishRpcServiceImpl implements TransportPublishRpcService {

    private final TransportAutoResendService transportAutoResendService;
    private final TransportMainService transportMainService;
    private final TransportAfterOrderDataService transportAfterOrderDataService;
    private final TransportEnterpriseLogService transportEnterpriseLogService;
    private final ThirdEnterpriseRemoteService thirdEnterpriseRemoteService;
    private final GoodCarPriceTransportService goodCarPriceTransportService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final DispatchCompanyService dispatchCompanyService;
    private final TytCityRemoteService tytCityRemoteService;
    private final DispatchCargoOwnerService dispatchCargoOwnerService;
    private final DispatchCooperativeService dispatchCooperativeService;
    private final SpecialGoodsBuilder specialGoodsBuilder;
    private final ThPriceService thPriceService;
    private final ExcellentGoodsCardRemoteService excellentGoodsCardRemoteService;
    private final ExcellentTransportChecker excellentTransportChecker;
    private final PromptProperties promptProperties;
    private final UserRemoteService userRemoteService;
    private final TytSequenceRemoteService tytSequenceRemoteService;
    private final CalcCommissionTecFeeService calcCommissionTecFeeService;
    private final CoverGoodsConfigService coverGoodsConfigService;
    private final TransportBackendService transportBackendService;

    private final TransportLabelJsonBuilder transportLabelJsonBuilder;
    private final TransportPublishService transportPublishService;
    private final TransportMainExtendService transportMainExtendService;
    private final TransportPopupTrackingLogService transportPopupTrackingLogService;
    private final ABTestRemoteService aBTestRemoteService;
    private final BiRemoteService biRemoteService;
    private final TransportExtendBuilder transportExtendBuilder;
    private final AutoResendTransportPostHandler autoResendTransportPostHandler;
    private final TransportPublishLogPostHandler transportPublishLogPostHandler;
    private final YmmTransportChecker ymmTransportChecker;
    private final SpecialTransportChecker specialTransportChecker;
    private final FixedTransportChecker fixedTransportChecker;
    private final SensitiveWordsChecker sensitiveWordsChecker;
    private final PublishPhoneChecker publishPhoneChecker;
    private final AllowPriceChecker allowPriceChecker;
    private final BackendTransportChecker backendTransportChecker;
    private final PublishPermissionChecker publishPermissionChecker;
    private final CarpoolTransportChecker carpoolTransportChecker;
    private final ActivityChecker activityChecker;
    private final AddressChecker addressChecker;
    private final UserPublishLimitChecker userPublishLimitChecker;
    private final UserAuthChecker userAuthChecker;
    private final PersonalDuplicateChecker personalDuplicateChecker;
    private final InvoiceTransportChecker invoiceTransportChecker;
    private final BaseTransportBuilder baseTransportBuilder;
    private final AddDispatchOwnerPostHandler addDispatchOwnerPostHandler;
    private final AddTransportHistoryHandler addTransportHistoryHandler;
    private final ExcellentPermissionPostHandler excellentPermissionPostHandler;
    private final AddMbCargoPostHandler addMbCargoPostHandler;
    private final AddMachineTypePostHandler addMachineTypePostHandler;
    private final BiTrickingPostHandler biTrickingPostHandler;
    private final InvoiceTransportSaveLogPostHandler invoiceTransportSaveLogPostHandler;
    private final SpecialtAutoAssignPostHandler specialtAutoAssignPostHandler;
    private final PublishPermissionPostHandler publishPermissionPostHandler;
    private final CommissionTransportPostHandler commissionTransportPostHandler;
    private final AddExtraRefreshPostHandler addExtraRefreshPostHandler;
    private final SendPublishMQPostHandler sendPublishMQPostHandler;
    private final InvoiceTransportAssignPostHandler invoiceTransportAssignPostHandler;
    private final ExposureCardGiveawayPostHandler exposureCardGiveawayPostHandler;
    private final CommissionTecFeeBuilder commissionTecFeeBuilder;
    private final AutoConvertExcellentGoodsChecker autoConvertExcellentGoodsChecker;


    private static final BigDecimal DEFAULT_ENTERPRISE_TAX_RATE = new BigDecimal("6.6");
    private final static BigDecimal FIFTY = new BigDecimal(50);

    @Override
    public TransportPublishVO transportPublish(PublishDTO publishDTO) {
        PublishBO publishBO = new PublishBO();
        BeanUtils.copyProperties(publishDTO, publishBO);

        PublishProcessBO publishProcessBO = new PublishProcessBO();
        publishProcessBO.setPublishBO(publishBO);
        // 获取当前登录用户信息
        LoginUserDTO loginUser = LoginHelper.getRequiredLoginUser();
        UserRpcVO user = userRemoteService.getUser(loginUser.getUserId());
        publishProcessBO.setUser(user);

        publishProcessBO.setBaseParam(LoginHelper.getBaseParam());


        // 先判断是否是当天发布的货源还是历史货源
        TransportMainDO oldMain = getOldTransportMain(publishProcessBO);
        TransportMainExtendDO oldMainExtend = getOldTransportMainExtend(oldMain);
        publishProcessBO.setOldMain(oldMain);
        publishProcessBO.setOldMainExtend(oldMainExtend);
        // 判断是否是小程序货源
        TransportBackendDO transportBackend = getBackendTransport(publishBO);
        publishProcessBO.setTransportBackend(transportBackend);

        // 先将需要转换的参数都转换完成
        buildConvert(publishBO, user);
        log.info("发布货源参数转换完成,publishBO:{}", JSONUtil.toJsonStr(publishBO));

        // 拦截校验及添加部分货源参数
        checkAssert(publishProcessBO);

        // 组装发货实体
        TransportMainDO transportMain = createTransportMain(publishBO, user, oldMain);
        publishProcessBO.setTransportMain(transportMain);
        TransportMainExtendDO mainExtend = createTransportMainExtend(publishProcessBO);
        publishProcessBO.setMainExtend(mainExtend);

        // 设置秒抢货源
        publishProcessBO.setSeckillGoods(transportExtendBuilder.checkIsSeckillGoods(transportMain, mainExtend.getCommissionScore()));


        // 组装完发货实体后的后置处理
        dealOfCreated(publishProcessBO);

        // 处理完成后再同步一下数据
        TransportDO transport = new TransportDO();
        TransportExtendDO transportExtend = new TransportExtendDO();
        BeanUtils.copyProperties(transportMain, transport);
        BeanUtils.copyProperties(mainExtend, transportExtend);
        publishProcessBO.setTransport(transport);
        publishProcessBO.setTransportExtend(transportExtend);

        // 发布货源
        transportPublishService.publishing(publishProcessBO);
        //发货完成后的后置处理
        afterPublish(publishProcessBO);
        // 组装返回值
        return buildReturnVO(publishProcessBO);


    }

    /**
     * 组装返回值
     *
     * @param publishProcessBO
     */
    private TransportPublishVO buildReturnVO(PublishProcessBO publishProcessBO) {
        TransportDO transport = publishProcessBO.getTransport();
        TransportPublishVO transportPublishVO = new TransportPublishVO();
        BeanUtils.copyProperties(transport, transportPublishVO);
        transportPublishVO.setTsId(transport.getId());
        transportPublishVO.setHasDestDetail((StringUtils.isNotBlank(transport.getDestDetailAdd()) && !Objects.equals(transport.getDestDetailAdd(), transport.getDestArea())) ? 1 : 0);
        return transportPublishVO;

    }

    private TransportMainExtendDO getOldTransportMainExtend(TransportMainDO oldTransportMain) {
        if (oldTransportMain != null) {
            return transportMainExtendService.getBySrcMsgId(oldTransportMain.getSrcMsgId());
        }
        return null;
    }

    private void afterPublish(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO oldTransportMain = publishProcessBO.getOldMain();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        UserRpcVO user = publishProcessBO.getUser();

        // 记录抽佣数据
        commissionTransportPostHandler.handler(publishProcessBO, true);
        // 如果是运满满货源，同步添加到运满满关系表中
        addMbCargoPostHandler.saveMbCargoSyncInfo(publishBO, transportMain);
        // 添加到个人货主
        addDispatchOwnerPostHandler.handler(publishProcessBO);
        //开票货源记录发布时货主的企业信息
        invoiceTransportSaveLogPostHandler.saveForPublish(transportMain, publishBO);
        // 如果是指派货源，调用履约的派单接口
        invoiceTransportAssignPostHandler.invoiceAssignCar(publishProcessBO.getTransportMain(), publishProcessBO.getPublishBO());
        // 扣减发货次数
        publishPermissionChecker.checkPublishPermission(publishProcessBO, true);
        // 更新用户发货次数(user已经将这个功能集成到)
//        publishPermissionPostHandler.updateAuthPermissionUsed(publishProcessBO);
        // 保存到历史表
        addTransportHistoryHandler.handler(transportMain);
        // 如果是优车货源，扣减优车发货次数并更新优车发货卡状态
        excellentPermissionPostHandler.handler(publishProcessBO);
        // 更新发布次数及月发布次数
        publishPermissionPostHandler.updatePublishNum(publishBO, transportMain);
        // 如果是待审核货名，进入后台的待审核列表
        addMachineTypePostHandler.saveMachineType(publishProcessBO);
        // 货源诊断
        addExtraRefreshPostHandler.handler(oldTransportMain, transportMain);
        // 如果勾选自动重发，添加到自动重发记录
        autoResendTransportPostHandler.saveAutoResendRecord(publishBO.getIsAutoResend(), user.getId(), transportMain.getSrcMsgId());
        // 记录埋点数据
        biTrickingPostHandler.savePublishTricking(publishProcessBO, publishBO);
        // 记录发货记录
        transportPublishLogPostHandler.handler(publishProcessBO);
        // 专车自动派单,发送mq
        specialtAutoAssignPostHandler.specialAssign(transportMain);
        // 货源发布后发送mq
        sendPublishMQPostHandler.handler(publishProcessBO);
        // 校验是否符合曝光卡发放规则
        exposureCardGiveawayPostHandler.handler(publishProcessBO);
    }


    /**
     * 发货时判断是否是小程序货源
     *
     * @param publishBO
     * @return
     */
    private TransportBackendDO getBackendTransport(PublishBO publishBO) {
        TransportBackendDO transportBackend = null;
        if (publishBO.getBackendId() != null && !Objects.equals(publishBO.getBackendId(), YesOrNoEnum.NO.getId().longValue())) {
            transportBackend = transportBackendService.getById(publishBO.getBackendId());
        }
        if (transportBackend == null && publishBO.getSrcMsgId() != null && Objects.equals(publishBO.getIsBackendTransport(), YesOrNoEnum.YES.getId())) {
            transportBackend = transportBackendService.selectBySrcMsgId(publishBO.getSrcMsgId());
        }
        return transportBackend;
    }

    private TransportMainExtendDO createTransportMainExtend(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        TransportMainExtendDO mainExtend = new TransportMainExtendDO();
        mainExtend.setUseCarType(publishBO.getUseCarType());
        mainExtend.setPriceType(publishBO.getPriceType());
        mainExtend.setSuggestMinPrice(publishBO.getSuggestMinPrice());
        mainExtend.setSuggestMaxPrice(publishBO.getSuggestMaxPrice());
        if (Objects.equals(publishBO.getExcellentGoodsTwo(), YesOrNoEnum.YES.getId())) {
            mainExtend.setSuggestMinPrice(publishBO.getFixPriceMin());
            mainExtend.setSuggestMaxPrice(publishBO.getFixPriceMax());
        }
        mainExtend.setCreateTime(transportMain.getCtime());
        mainExtend.setModifyTime(transportMain.getMtime());
        // 获取好货模型分数
        BiGoodModelResult goodModel = biRemoteService.getGoodModel(transportMain);
        if (goodModel != null) {
            mainExtend.setGoodModelScore(goodModel.getScore());
            mainExtend.setGoodModelLevel(goodModel.getLevel());
            mainExtend.setLimGoodModelScore(goodModel.getLim_score());
            mainExtend.setLimGoodModelLevel(goodModel.getLim_level());
            publishProcessBO.setBiGoodModelResult(goodModel);
        }
        // 获取好货运价模型
        if (StringUtils.isNotBlank(transportMain.getPrice())) {
            BiGoodModelResult goodsModelPrice = biRemoteService.getGoodsModelPrice(transportMain);
            if (goodsModelPrice != null) {
                mainExtend.setCommissionScore(goodsModelPrice.getScore());
            }
        }

        return mainExtend;
    }

    private void dealOfCreated(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        TransportMainDO transportMain = publishProcessBO.getTransportMain();
        // 校验货源是否是重货
        personalDuplicateChecker.check(publishProcessBO, publishBO.getAssignCarTel());

        // 组装货源标签
        transportLabelJsonBuilder.build(publishProcessBO);


        // 计算货源的抽佣信息
        // 如果版本号>=6670 & 代调账号发的专车非平台，使用客户端传的技术服务费，后面填价、加价、直接发布均不重新计算佣金
        if (commissionTecFeeBuilder.isNeedCalcCommission(publishProcessBO)) {
            boolean isExcellentGoodsTwo = Objects.equals(transportMain.getExcellentGoodsTwo(), ExcellentGoodsTwoEnum.YES.getCode());
            TecServiceFeeConfigComputeResult configToComputeResult = calcCommissionTecFeeService.makeTecServiceFeeData(publishProcessBO, isExcellentGoodsTwo);
            publishProcessBO.setConfigToComputeResult(configToComputeResult);
            if (configToComputeResult != null) {
                publishProcessBO.setCommissionTransport(true);
            }
        } else {
            TransportLabelJson labelJson = JSONUtil.toBean(transportMain.getLabelJson(), TransportLabelJson.class);
            // 如果佣金不为空且>0，算抽佣
            if (transportMain.getTecServiceFee() != null && transportMain.getTecServiceFee().compareTo(BigDecimal.ZERO) > 0) {
                labelJson.setCommissionTransport(1);
                publishProcessBO.setCommissionTransport(true);
                publishProcessBO.setConfigToComputeResult(commissionTecFeeBuilder.getManualTecServiceFeeResult(transportMain, publishProcessBO.getMainExtend()));
            } else { // 不填或=0，不抽佣
                labelJson.setCommissionTransport(0);
                transportMain.setTecServiceFee(BigDecimal.ZERO); // 必须设置为0，为null不会更新数据库
            }

            // 如果使用客户端传的技术服务费，则打标
            labelJson.setIsManualTecServiceFee(1);
            transportMain.setLabelJson(JSONUtil.toJsonStr(labelJson));
        }
    }

    /**
     * 发货组装处理
     *
     * @param publishBO
     * @param user
     */
    private TransportMainDO createTransportMain(PublishBO publishBO, UserRpcVO user, TransportMainDO oldTransportMain) {
        // 组装实体
        TransportMainDO transportMain = buildTransportMain(publishBO, user, oldTransportMain);
        // 设置用户信用积分及等级
        ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(user.getId());
        transportMain.setTotalScore(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getTotalScore).orElse(BigDecimal.ZERO));
        transportMain.setRankLevel(Optional.ofNullable(userCreditInfo).map(ApiDataUserCreditInfoRpcVO::getRankLevel).orElse(1));

        // 用户成交单数
        UserSubRpcVO userSub = userRemoteService.getUserSubById(user.getId());
        transportMain.setTradeNum(Optional.ofNullable(userSub).map(UserSubRpcVO::getDealNum).orElse(0));

        // 设置捂货时间
        setPriorityRecommendExpireTime(publishBO, transportMain, oldTransportMain);

        return transportMain;
    }

    private void setPriorityRecommendExpireTime(PublishBO publishBO, TransportMainDO transportMain, TransportMainDO oldTransportMain) {

        if (Objects.equals(publishBO.getPriorityRecommend(), YesOrNoEnum.YES.getId())) {
            // 当日货物编辑发布 保持原来捂货过期时间不变，新货源和历史货源重新发货 重新计算
            if (Objects.equals(publishBO.getPublishStyle(), TODAY_PUBLISH.getCode())) {
                transportMain.setPriorityRecommendExpireTime(oldTransportMain.getPriorityRecommendExpireTime());
            } else {
                Integer xSecond = coverGoodsConfigService.selectXTime();
                Date date = DateUtils.addSeconds(transportMain.getPubDate(), xSecond);
                transportMain.setPriorityRecommendExpireTime(date);
            }
        }
    }

    private TransportMainDO buildTransportMain(PublishBO publishBO, UserRpcVO user, TransportMainDO oldTransportMain) {
        TransportMainDO transportMain = new TransportMainDO();

        BaseParamDTO baseParam = LoginHelper.getBaseParam();

        // 基础数据
        transportMain.setIsDelete(0);
        transportMain.setPlatId(baseParam.getClientSign());
        transportMain.setIsShow(publishBO.getIsShow() != null ? publishBO.getIsShow() : YesOrNoEnum.YES.getId());
        transportMain.setDisplayType(YesOrNoEnum.YES.getId().toString());
        transportMain.setStatus(YesOrNoEnum.YES.getId());
        transportMain.setIsDisplay(YesOrNoEnum.YES.getId());
        transportMain.setIsShow(publishBO.getIsShow() != null ? publishBO.getIsShow() : YesOrNoEnum.YES.getId());


        // 0待接单 1有人支付成功 （货主的待同意）2装货中（车主是待装货 ）3车主装货完成 4系统装货完成 5异常上报
        transportMain.setInfoStatus(YesOrNoEnum.NO.getId().toString());
        // 生成订单号
        transportMain.setTsOrderNo(tytSequenceRemoteService.getSequence("tyt_waybill_nbr"));

        // 用户数据
        transportMain.setUserId(user.getId());
        if (StringUtils.isNotBlank(user.getTrueName()) && StringUtils.isNotBlank(user.getIdCard())) {
            transportMain.setUserShowName(user.getTrueName().charAt(0) + IdCardUtil.getCallGender(user.getIdCard()));
        } else {
            transportMain.setUserShowName(user.getUserName());
        }
        transportMain.setUploadCellphone(user.getCellPhone());
        transportMain.setIsCar(user.getIsCar());
        transportMain.setVerifyPhotoSign(user.getVerifyPhotoSign() > 1 ? 0 : user.getVerifyPhotoSign());
        transportMain.setRegTime(user.getCtime());
        transportMain.setNickName(makeNickName(publishBO.getExcellentGoods(), user));


        // 地址数据
        transportMain.setStartProvinc(publishBO.getStartProvinc());
        transportMain.setStartCity(publishBO.getStartCity());
        transportMain.setStartArea(publishBO.getStartArea());
        transportMain.setStartPoint(publishBO.getStartPoint());
        transportMain.setStartDetailAdd(publishBO.getStartDetailAdd());
        transportMain.setDestProvinc(publishBO.getDestProvinc());
        transportMain.setDestCity(publishBO.getDestCity());
        transportMain.setDestArea(publishBO.getDestArea());
        transportMain.setDestPoint(publishBO.getDestPoint());
        transportMain.setDestDetailAdd(publishBO.getDestDetailAdd());

        transportMain.setStartCoord(publishBO.getStartCoordX().toString() + "," + publishBO.getStartCoordY().toString());
        transportMain.setDestCoord(publishBO.getDestCoordX().toString() + "," + publishBO.getDestCoordY().toString());
        transportMain.setStartCoordX(publishBO.getStartCoordX());
        transportMain.setStartCoordY(publishBO.getStartCoordY());
        transportMain.setDestCoordX(publishBO.getDestCoordX());
        transportMain.setDestCoordY(publishBO.getDestCoordY());

        transportMain.setStartLatitude(publishBO.getStartLatitude());
        transportMain.setStartLongitude(publishBO.getStartLongitude());
        transportMain.setDestLatitude(publishBO.getDestLatitude());
        transportMain.setDestLongitude(publishBO.getDestLongitude());
        if (publishBO.getDistance() != null) {
            transportMain.setDistance(publishBO.getDistance());

            if (Objects.equals(baseParam.getClientSign(), ClientSignEnum.ANDROID_CAR.getCode()) || Objects.equals(baseParam.getClientSign(), ClientSignEnum.ANDROID_GOODS.getCode())) {
                transportMain.setAndroidDistance(publishBO.getDistance());
            }
            if (Objects.equals(baseParam.getClientSign(), ClientSignEnum.IOS_CAR.getCode()) || Objects.equals(baseParam.getClientSign(), ClientSignEnum.IOS_GOODS.getCode())) {
                transportMain.setIosDistance(publishBO.getDistance());
            }
        }

        //货源数据

        String taskcontent = TytStrUtil.hidePhone(publishBO.getTaskContent());
        taskcontent = taskcontent.replaceAll("'", "-").replaceAll("\"", "--");
        transportMain.setTaskContent(taskcontent);
        transportMain.setRemark(TytStrUtil.hidePhone(publishBO.getRemark()));
        transportMain.setMachineRemark(TytStrUtil.hidePhone(publishBO.getMachineRemark()));
        transportMain.setInfoFee(publishBO.getInfoFee());


        transportMain.setTel(publishBO.getTel());
        transportMain.setTel3(publishBO.getTel3());
        transportMain.setTel4(publishBO.getTel4());
        transportMain.setPrice(publishBO.getPrice());
        transportMain.setIsInfoFee(YesOrNoEnum.YES.getId().toString());
        //v6120新增调车数量
        transportMain.setShuntingQuantity(publishBO.getShuntingQuantity());
        //v6140 货源类型（电议1，一口价2）
        transportMain.setPublishType(publishBO.getPublishType());
        transportMain.setRefundFlag(publishBO.getRefundFlag());
        transportMain.setSourceType(publishBO.getSourceType());
        transportMain.setTecServiceFee(publishBO.getTecServiceFee());
        transportMain.setExcellentCardId(publishBO.getExcellentCardId());
        // 重新构造税率
        transportMain.setInvoiceTransport(publishBO.getInvoiceTransport());
        if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId())) {
            InvoiceTransportTaxInfoDTO additionalAndTaxRate = invoiceTransportChecker.getAdditionalAndTaxRate(user.getId(), publishBO.getPrice(), publishBO.getInvoiceSubjectId());
            transportMain.setEnterpriseTaxRate(additionalAndTaxRate.getEnterpriseTaxRate());
            transportMain.setAdditionalPrice(additionalAndTaxRate.getAdditionalPrice() == null ? null : additionalAndTaxRate.getAdditionalPrice().toString());
        }

        //货物数据
        transportMain.setBeginLoadingTime(publishBO.getBeginLoadingTime());
        transportMain.setBeginUnloadTime(publishBO.getBeginUnloadTime());
        transportMain.setLoadingTime(publishBO.getLoadingTime());
        transportMain.setUnloadTime(publishBO.getUnloadTime());
        transportMain.setCarMinLength(publishBO.getCarMinLength());
        transportMain.setCarMaxLength(publishBO.getCarMaxLength());
        transportMain.setWorkPlaneMinHigh(publishBO.getWorkPlaneMinHigh());
        transportMain.setWorkPlaneMaxHigh(publishBO.getWorkPlaneMaxHigh());
        transportMain.setWorkPlaneMinLength(publishBO.getWorkPlaneMinLength());
        transportMain.setWorkPlaneMaxLength(publishBO.getWorkPlaneMaxLength());
        transportMain.setClimb(publishBO.getClimb());
        transportMain.setCarStyle(publishBO.getCarStyle());
        transportMain.setExclusiveType(publishBO.getExclusiveType());
        transportMain.setWeight(StringUtils.defaultIfEmpty(publishBO.getWeight(), null));
        transportMain.setLength(StringUtils.defaultIfEmpty(publishBO.getLength(), null));
        transportMain.setWide(StringUtils.defaultIfEmpty(publishBO.getWide(), null));
        transportMain.setHigh(StringUtils.defaultIfEmpty(publishBO.getHigh(), null));
        transportMain.setReferWeight(TransportUtil.getReferNumber(transportMain.getWeight()));
        transportMain.setReferLength(TransportUtil.getReferNumber(transportMain.getLength()));
        transportMain.setReferWidth(TransportUtil.getReferNumber(transportMain.getWide()));
        transportMain.setReferHeight(TransportUtil.getReferNumber(transportMain.getHigh()));
        transportMain.setIsSuperelevation(publishBO.getIsSuperelevation());
        transportMain.setType(publishBO.getType());
        transportMain.setBrand(publishBO.getBrand());
        transportMain.setGoodTypeName(publishBO.getGoodTypeName());
        transportMain.setGoodNumber(publishBO.getGoodNumber());
        transportMain.setIsStandard(publishBO.getIsStandard());
        transportMain.setMatchItemId(publishBO.getMatchItemId());
        transportMain.setCarType(publishBO.getCarType());
        transportMain.setCarLength(publishBO.getCarLength());
        transportMain.setSpecialRequired(publishBO.getSpecialRequired());
        transportMain.setCarLengthLabels(publishBO.getCarLengthLabels());

        // 补充新版标准化货物信息的参考值，必须在生成hashcode之前
        baseTransportBuilder.addReferTransportNewInfo(transportMain);

        transportMain.setTyreExposedFlag(StringUtils.isNotBlank(publishBO.getTyreExposedFlag()) ? publishBO.getTyreExposedFlag() : "0");


        // 时间数据
        Date date = new Date();
        transportMain.setCtime(date);
        transportMain.setMtime(date);
        transportMain.setPubDate(date);
        transportMain.setPubTime(DateUtil.formatTime(date));
        transportMain.setReleaseTime(date);

        // 标识数据
        transportMain.setExcellentGoods(publishBO.getExcellentGoods());
        transportMain.setExcellentGoodsTwo(publishBO.getExcellentGoodsTwo());
        transportMain.setDriverDriving(publishBO.getDriverDriving());
        transportMain.setClientVersion(baseParam.getClientVersion());
        transportMain.setGuaranteeGoods(publishBO.getGuaranteeGoods());
        // 专车字段
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBO.getExcellentGoods())) {
            transportMain.setLoadCellPhone(publishBO.getLoadCellPhone());
            transportMain.setUnloadCellPhone(publishBO.getUnloadCellPhone());
            // 签约合作商字段赋值处理
            if (Objects.nonNull(publishBO.getCargoOwnerId()) && publishBO.getCargoOwnerId() != 0) {
                transportMain.setCargoOwnerId(publishBO.getCargoOwnerId());
            } else {
                DispatchCargoOwnerDO cargoOwner = dispatchCargoOwnerService.selectSignedByUserId(user.getId());
                if (Objects.nonNull(cargoOwner)) {
                    transportMain.setCargoOwnerId(cargoOwner.getCooperativeId());
                }
            }
        }

        // 新发货源和历史货源的区别的地方
        if (Objects.equals(publishBO.getPublishStyle(), PublishStyleEnum.TODAY_PUBLISH.getCode())) {
            transportMain.setFirstPublishType(oldTransportMain.getPublishType());
            transportMain.setResend(oldTransportMain.getResend());
        } else {
            transportMain.setFirstPublishType(transportMain.getPublishType());
            transportMain.setResend(0);
        }

        // hashcode,必须先获取标准货物信息才能生成hashCode
        transportMain.setHashCode(TransportUtil.getNewHashCode(transportMain));

        // 暂时不知道什么用处
        transportMain.setPcOldContent(transportMain.getStartPoint().trim() + "---" + transportMain.getDestPoint().trim() + " " + transportMain.getTaskContent().trim());
        transportMain.setSource(0);
        transportMain.setVerifyFlag(1);
        transportMain.setLinkman(StringUtils.isNotBlank(publishBO.getLinkMan()) ? publishBO.getLinkMan() : "用户" + user.getId());
        transportMain.setPubQq(user.getQq());
        transportMain.setUserType(1);
        transportMain.setUserPart(user.getUserPart());
        transportMain.setSortType(0);
        return transportMain;
    }

    public String makeNickName(Integer excellentGoods, UserRpcVO user) {
        if (user.getId() == null) {
            return null;
        }
        boolean isShowUserName = false;
        if (Objects.equals(excellentGoods, ExcellentGoodsEnums.EXCELLENT.getCode())) {
            //如果是优车并且该货主不在AB测试中才将昵称赋值为X老板
            Integer userType = aBTestRemoteService.getUserType(YOUCHE_IS_SHOW_USERNAMEABTEST_CODE, user.getId());
            if (userType == 1) {
                isShowUserName = true;
            }
        } else {
            Integer type = aBTestRemoteService.getUserType(SHOW_BOSS_NICK_NAME, user.getId());
            if (type == 0) {
                isShowUserName = true;
            }
        }
        if (isShowUserName) {
            return StringUtils.isBlank(user.getUserName()) ? "用户" + user.getId() : user.getUserName();
        } else {
            String surname = StringUtils.isBlank(user.getTrueName()) ? "" : Character.toString(user.getTrueName().charAt(0));
            return surname + "老板";
        }
    }


    private void checkAssert(PublishProcessBO publishProcessBO) {
        PublishBO publishBO = publishProcessBO.getPublishBO();
        UserRpcVO user = publishProcessBO.getUser();
        TransportBackendDO transportBackend = publishProcessBO.getTransportBackend();

        // 校验地址参数
        addressChecker.checkPublishAddress(publishBO);
        // 用户实名认证校验
        userAuthChecker.checkUserAuth(publishBO, user);
        // 发货限制校验
        userPublishLimitChecker.check(user);
        // 一口价货源限制校验
        fixedTransportChecker.checkFixedTransport(publishBO);
        // 校验货物装卸货时间、订金
        checkBaseParams(publishBO);
        // 校验货物内容(备注)是否处罚敏感词
        sensitiveWordsChecker.checkSensitiveWords(publishBO, user);
        // 运满满货源校验
        ymmTransportChecker.checkPublish(publishBO, user);
        // 专车发货的校验（合作商、专车运价）
        specialTransportChecker.specialCheck(publishBO, user);
        //验证电话是否为本人联系电话，人工派单不进行验证；
        publishPhoneChecker.checkPhone(publishBO, user);
        // 校验用户发货权益
        publishPermissionChecker.checkPublishPermission(publishProcessBO, false);
        // 校验优车权益限制
        excellentTransportChecker.checkExcellentGoods(publishBO, user);
        // 校验运费
        allowPriceChecker.checkPublishPrice(publishBO, user.getId());
        // 校验开票货源
        invoiceTransportChecker.checkInvoiceTransport(publishBO, user);
        // 小程序货源校验
        backendTransportChecker.checkBackend(publishBO, transportBackend, user);
        // 拼车校验
        carpoolTransportChecker.check(publishBO, user);
        // 6510拉新裂变活动
        activityChecker.checkActivity(user);
        // 发布优车2.0电议货源次数校验
        autoConvertExcellentGoodsChecker.checkExcellentGoodsTele(publishProcessBO);
    }


    /**
     * 获取历史货源信息
     *
     * @param processBO
     * @return
     */
    private TransportMainDO getOldTransportMain(PublishProcessBO processBO) {
        PublishBO publishBO = processBO.getPublishBO();
        Long oldSrcMsgId = publishBO.getSrcMsgId();
        TransportMainDO oldTransportMain = null;

        processBO.setOptEnum(PublishOptEnum.PUBLISH);

        if (oldSrcMsgId != null && oldSrcMsgId > 0L) {
            oldTransportMain = transportMainService.getTransportMainForId(oldSrcMsgId);
            if (oldTransportMain == null) {
                publishBO.setPublishStyle(PublishStyleEnum.NEW_PUBLISH.getCode());
            } else {
                if (oldTransportMain.getStatus() == 4) {
                    publishBO.setPublishStyle(PublishStyleEnum.NEW_PUBLISH.getCode());
                    processBO.setOptEnum(PublishOptEnum.PUBLISH);

                } else if ((oldTransportMain.getCtime().after(DateUtil.beginOfDay(new Date())))) {

                    publishBO.setPublishStyle(TODAY_PUBLISH.getCode());
                    processBO.setOptEnum(PublishOptEnum.EDIT);
                } else {
                    publishBO.setPublishStyle(HISTORY_PUBLISH.getCode());
                }
            }
        } else {
            publishBO.setPublishStyle(PublishStyleEnum.NEW_PUBLISH.getCode());
        }
        return oldTransportMain;
    }


    /**
     * 校验货物参数
     *
     * @param publishBO
     */
    private void checkBaseParams(PublishBO publishBO) {

        // 校验装卸货时间
        if ((publishBO.getLoadingTime() != null && new Date().compareTo(publishBO.getLoadingTime()) > 0) || (publishBO.getUnloadTime() != null && new Date().compareTo(publishBO.getUnloadTime()) > 0)) {
            throw new BusinessException(GoodsErrorCode.LOADING_TIME_EXPIRE);
        }

        // 订金在不可退的情况下，订金不能大于等于运费
        if (Objects.equals(publishBO.getRefundFlag(), 0)
                && publishBO.getInfoFee() != null && StringUtils.isNotBlank(publishBO.getPrice())) {
            BigDecimal price = new BigDecimal(publishBO.getPrice());
            if (publishBO.getInfoFee().compareTo(price) >= 0) {
                throw new BusinessException(GoodsErrorCode.INFO_FEE_TOO_HIGH);
            }
        }
    }

    private void buildAddress(PublishBO publishBO) {

        String startCity = publishBO.getStartCity();
        String startArea = publishBO.getStartArea();
        String destCity = publishBO.getDestCity();
        String destArea = publishBO.getDestArea();

        if (StringUtils.isBlank(startCity)) {
            startCity = startArea;
        }
        if (StringUtils.isBlank(destCity)) {
            destCity = destArea;
        }
        TytCityVo startCityVo = null;
        if (StringUtils.isNotBlank(startCity) && StringUtils.isNotBlank(startArea)) {
            startCityVo = tytCityRemoteService.getRegxByName(startCity, startArea, "3");
            if (startCityVo == null) {
                // 提级匹配，比如澳门等
                startArea = startCity;
                startCityVo = tytCityRemoteService.getRegxByName(startArea, startCity, "3");
            }
        }
        if (startCityVo == null && publishBO.getStartCoordX() != null && publishBO.getStartCoordY() != null) {
            String startPx = publishBO.getStartCoordX().stripTrailingZeros().toPlainString();
            String startPy = publishBO.getStartCoordY().stripTrailingZeros().toPlainString();
            startCityVo = tytCityRemoteService.getCityByXY(startPx, startPy, "3");
        }
        if (startCityVo != null) {
            publishBO.setStartProvinc(startCityVo.getProvinceName());
            publishBO.setStartCity(startCityVo.getCityName());
            publishBO.setStartArea(startCityVo.getAreaName());
            publishBO.setStartCoordX(new BigDecimal(startCityVo.getPx()));
            publishBO.setStartCoordY(new BigDecimal(startCityVo.getPy()));
        }
        TytCityVo destCityVo = null;
        if (StringUtils.isNotBlank(destCity) && StringUtils.isNotBlank(destArea)) {
            destCityVo = tytCityRemoteService.getRegxByName(destCity, destArea, "3");
            if (destCityVo == null) {
                // 提级匹配，比如澳门等
                destArea = destCity;
                destCityVo = tytCityRemoteService.getRegxByName(destArea, destCity, "3");
            }
        }
        if (destCityVo == null && publishBO.getDestCoordX() != null && publishBO.getDestCoordY() != null) {
            String destPx = publishBO.getDestCoordX().stripTrailingZeros().toPlainString();
            String destPy = publishBO.getDestCoordY().stripTrailingZeros().toPlainString();
            destCityVo = tytCityRemoteService.getCityByXY(destPx, destPy, "3");
        }
        if (destCityVo != null) {
            publishBO.setDestProvinc(destCityVo.getProvinceName());
            publishBO.setDestCity(destCityVo.getCityName());
            publishBO.setDestArea(destCityVo.getAreaName());
            publishBO.setDestCoordX(new BigDecimal(destCityVo.getPx()));
            publishBO.setDestCoordY(new BigDecimal(destCityVo.getPy()));
        }

        String startPoint = updateAddressPoint(publishBO.getStartProvinc(), publishBO.getStartCity(), publishBO.getStartArea());
        String destPoint = updateAddressPoint(publishBO.getDestProvinc(), publishBO.getDestCity(), publishBO.getDestArea());
        publishBO.setStartPoint(startPoint);
        publishBO.setDestPoint(destPoint);
    }


    private String updateAddressPoint(String province, String city, String area) {
        if (StringUtils.isNotBlank(city) && StringUtils.isNotBlank(area)) {
            if (city.contains(province)) {
                if (area.contains(city)) {
                    return area;
                } else {
                    return city + area;
                }
            } else {
                if (area.contains(city)) {
                    return province + area;
                } else {
                    return province + city + area;
                }
            }
        } else {
            if (StringUtils.isNotBlank(city)) {
                if (city.contains(province)) {
                    return city;
                } else {
                    return province + city;
                }
            } else if (StringUtils.isNotBlank(area)) {
                if (area.contains(province)) {
                    return area;
                } else {
                    return province + area;
                }
            } else {
                return province;
            }
        }
    }


    private void buildConvert(PublishBO publishBO, UserRpcVO user) {
        BaseParamDTO baseParam = LoginHelper.getBaseParam();
        // 因为pc端不能发优车货源，所以pc发的货可能没有excellentGoods字段，所以这里需要补上,默认是非优车货源
        if (publishBO.getExcellentGoods() == null || Objects.equals(baseParam.getClientSign(), ClientSignEnum.PC.getCode())) {
            publishBO.setExcellentGoods(ExcellentGoodsEnums.NORMAL.getCode());
            publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.NO.getCode());
        }
        // 如果客户端弹起了优车卡片，则将货源转为优车货源，并且转为优车2.0货源
        if (Objects.equals(publishBO.getGoodCarPriceTransport(), YesOrNoEnum.YES.getId())) {
            publishBO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
            publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
        }


        // 如果是开票货源，要将订金修改为退还状态（开票货源订金都为退还）
        if (Objects.equals(publishBO.getInvoiceTransport(), YesOrNoEnum.YES.getId())) {
            // 如果是专票，则将订金改为退还状态
            publishBO.setRefundFlag(YesOrNoEnum.YES.getId());
            // 开票货源如果指派车方的话定金强制修改为0并且不在找货大厅显示
            if (StringUtils.isNotBlank(publishBO.getAssignCarTel())) {
                publishBO.setInfoFee(BigDecimal.ZERO);
                publishBO.setIsShow(YesOrNoEnum.NO.getId());
            }
            // 如果是分段支付，要计算总价
            if (Objects.equals(publishBO.getPaymentsType(), YesOrNoEnum.YES.getId())) {
                BigDecimal prepaidPrice = publishBO.getPrepaidPrice() == null ? BigDecimal.ZERO : publishBO.getPrepaidPrice();
                BigDecimal collectedPrice = publishBO.getCollectedPrice() == null ? BigDecimal.ZERO : publishBO.getCollectedPrice();
                BigDecimal receiptPrice = publishBO.getReceiptPrice() == null ? BigDecimal.ZERO : publishBO.getReceiptPrice();
                BigDecimal allPrice = collectedPrice.add(prepaidPrice).add(receiptPrice);
                publishBO.setPrice(allPrice.toPlainString());

            }
        }
        // 如果是编辑发布，去除备注的固定文案
        if (StringUtils.isNotBlank(publishBO.getRemark()) && publishBO.getSrcMsgId() != null) {
            String remarkPrompt = tytConfigRemoteService.getStringValue(GOODS_DETAIL_REMARK_PROMPT);
            if (StringUtils.isNotBlank(remarkPrompt) && publishBO.getRemark().endsWith(remarkPrompt)) {
                publishBO.setRemark(StringUtils.remove(publishBO.getRemark(), remarkPrompt));
            }
        }
        // 如果符合条件，自动转优车货源
        autoConvertExcellentGoods(publishBO, user);

        // 校验及匹配出发地目的地地址
        buildAddress(publishBO);

        // 校验装卸货时间
        convertLoadingTime(publishBO);


        // 如果是优车2.0货源，就发一张优车发货卡
        if (Objects.equals(publishBO.getExcellentGoodsTwo(), ExcellentGoodsTwoEnum.YES.getCode())) {
            publishExcellentGoodsCard(publishBO, user);
        }
        // 开票货源转换,添加主体
        buildInvoiceTransport(publishBO);

        // 确定来源
        confirmSourceType(publishBO, user);

        // 标准货源
        if (publishBO.getMatchItemId() != null && publishBO.getMatchItemId() > 0 && publishBO.getMatchItemId() != 9999999) {
            publishBO.setIsStandard(YesOrNoEnum.NO.getId());
        } else {
            // 非标准货源
            publishBO.setIsStandard(YesOrNoEnum.YES.getId());
            if (publishBO.getMatchItemId() == null) {
                publishBO.setMatchItemId(-1);
            }
        }

        // 如是专车货源,计算专车运费及运费类型
        specialGoodsBuilder.build(publishBO, user);


        // 如果距离为空的话给个默认距离
//        if (publishBO.getDistance() == null) {
//            MapDictDto mapDictDto = new MapDictDto();
//            BeanUtils.copyProperties(publishBO, mapDictDto);
//            MapDictVo mapDict = mapDictRemoteService.getMapDict(mapDictDto);
//            publishBO.setDistance(BigDecimal.valueOf(mapDict.getDistance()).movePointLeft(2));
//        }


    }

    /**
     * 装卸货时间转换
     *
     * @param publishBO
     */
    private void convertLoadingTime(PublishBO publishBO) {
        if (publishBO.getLoadingTime() != null && publishBO.getLoadingTime().getTime() == 0) {
            publishBO.setLoadingTime(null);
        }
        if (publishBO.getBeginLoadingTime() != null && publishBO.getBeginLoadingTime().getTime() == 0) {
            publishBO.setBeginLoadingTime(null);
        }
        if (publishBO.getUnloadTime() != null && publishBO.getUnloadTime().getTime() == 0) {
            publishBO.setUnloadTime(null);
        }
        if (publishBO.getBeginUnloadTime() != null && publishBO.getBeginUnloadTime().getTime() == 0) {
            publishBO.setBeginUnloadTime(null);
        }

    }

    /**
     * 确定来源
     *
     * @param publishBO
     * @param user
     */
    private void confirmSourceType(PublishBO publishBO, UserRpcVO user) {

        if (publishBO.getSourceType() == null || publishBO.getSourceType() == 0) {
            publishBO.setSourceType(SourceTypeEnum.NORMAL.getCode());
        }
        if (ExcellentGoodsEnums.SPECIAL.getCode().equals(publishBO.getExcellentGoods())) {
            List<DispatchCompanyDO> companyDOList = dispatchCompanyService.selectByUserId(user.getId());
            if (CollUtil.isNotEmpty(companyDOList)) {
                SourceTypeEnum sourceTypeEnum = SourceTypeEnum.DISPATCH;
                DispatchCooperativeDO cooperative = dispatchCooperativeService.selectByName("宏信建发");
                if (Objects.nonNull(cooperative) && cooperative.getId().equals(publishBO.getCargoOwnerId())) {
                    sourceTypeEnum = SourceTypeEnum.HONGXIN;
                }
                publishBO.setSourceType(sourceTypeEnum.getCode());
            }
        }

    }

    private void buildInvoiceTransport(PublishBO publishBO) {
        String invoiceSubjectData = tytConfigRemoteService.getStringValue(INVOICE_SUBJECT_DATA, "1,JCZY");
        String[] split = invoiceSubjectData.split(COMMA);
        String subjectId = split[0];
        String subjectCode = split[1];
        if (publishBO.getInvoiceSubjectId() == null || StringUtils.isBlank(publishBO.getServiceProviderCode())) {
            publishBO.setInvoiceSubjectId(Long.valueOf(subjectId));
            publishBO.setServiceProviderCode(subjectCode);
        }
    }

    private void publishExcellentGoodsCard(PublishBO publishBO, UserRpcVO user) {
        GainExcellentGoodsCardRpcDTO goodsCardRpcDTO = new GainExcellentGoodsCardRpcDTO();
        goodsCardRpcDTO.setUserId(user.getId());
        goodsCardRpcDTO.setNew(false);
        Long excellentGoodsCardId = excellentGoodsCardRemoteService.gainExcellentGoodsCard(goodsCardRpcDTO);
        publishBO.setExcellentCardId(excellentGoodsCardId);

    }

    /**
     * 自动转优车货源
     *
     * @param publishBO
     */
    private void autoConvertExcellentGoods(PublishBO publishBO, UserRpcVO user) {
        // 是否是用户手动勾选优车发货
        publishBO.setUserManualExcellentGoods(Objects.equals(publishBO.getGoodCarPriceTransport(), 1));
        // 非专车货源如果满足条件，自动转成优车2.0货源
        if (Objects.equals(publishBO.getExcellentGoods(), ExcellentGoodsEnums.NORMAL.getCode())) {
            // 如果符合自动转成优车2.0条件，就打上标识，后续按照优车2.0来处理
            TransportCarryDTO transportCarryDTO = buildTransportCarryDTO(publishBO, user);
            if (goodCarPriceTransportService.checkTransportIsGoodCarPriceTransport(transportCarryDTO)) {
                log.info("自动转优车2.0货源成功，userId:{}", user.getId());
                publishBO.setGoodCarPriceTransport(1);
                publishBO.setAutomaticGoodCarPriceTransport(true);
                publishBO.setExcellentGoods(ExcellentGoodsEnums.EXCELLENT.getCode());
                publishBO.setExcellentGoodsTwo(ExcellentGoodsTwoEnum.YES.getCode());
            }
        }
    }

    private TransportCarryDTO buildTransportCarryDTO(PublishBO publishBO, UserRpcVO user) {
        TransportCarryDTO transportCarryDTO = new TransportCarryDTO();
        transportCarryDTO.setStartProvince(publishBO.getStartProvinc());
        transportCarryDTO.setStartCity(publishBO.getStartCity());
        transportCarryDTO.setStartArea(publishBO.getStartArea());
        transportCarryDTO.setDestProvince(publishBO.getDestProvinc());
        transportCarryDTO.setDestCity(publishBO.getDestCity());
        transportCarryDTO.setDestArea(publishBO.getDestArea());
        transportCarryDTO.setGoodsWeight(publishBO.getWeight());
        transportCarryDTO.setGoodsLength(publishBO.getLength());
        transportCarryDTO.setGoodsWide(publishBO.getWide());
        transportCarryDTO.setGoodsHigh(publishBO.getHigh());
        transportCarryDTO.setUserId(user.getId());
        transportCarryDTO.setDistance(publishBO.getDistance() != null ? publishBO.getDistance().toString() : "0");
        transportCarryDTO.setGoodTypeName(publishBO.getGoodTypeName());
        transportCarryDTO.setPrice(publishBO.getPrice());
        transportCarryDTO.setPublishType(publishBO.getPublishType());
        transportCarryDTO.setOldSrcMsgId(publishBO.getSrcMsgId());

        return transportCarryDTO;
    }

    /**
     * 返回自动重发剩余次数
     */
    @Override
    public int getAutoResendRemainTimes(Long userId) {
        return transportAutoResendService.getRemainTimes(userId);
    }

    /**
     * 我的货源页点击直接发布是否弹出出价弹窗接口
     *
     * @param srcMsgId 货源id
     */
    @Override
    public PopUpPriceBoxDTO isPopupPriceBox(Long srcMsgId) {
        log.info("直接发布是否弹出出价弹窗，开始，srcMsgId:{}", srcMsgId);
        PopUpPriceBoxDTO priceBoxDTO = new PopUpPriceBoxDTO();
        priceBoxDTO.setShowDialogType(0); // 默认旧弹窗
        priceBoxDTO.setPrompt("将生成一条相同内容的信息，是否发布？");

        TransportMainDO transport = transportMainService.getById(srcMsgId);
        // 货源在发布中且未过期 或 是有价货源，不显示出价弹窗
        boolean isHistoryTransport = transport.getCtime().before(DateUtil.beginOfDay(new Date()));
        if (transport.getStatus() == 1 && !isHistoryTransport || TransportUtil.hasPrice(transport.getPrice())) {
            log.error("直接发布是否弹出出价弹窗，货源不符合条件，srcMsgId:{}", srcMsgId);
            return priceBoxDTO;
        }

        Long userId = LoginHelper.getRequiredLoginUser().getUserId();

        // 获取推荐价格
        // 优先级1:取本人上一票相同货源成交价，相同标准：出发地、目的地、货物名称、长宽高重、调车数量均一致
        log.info("直接发布是否弹出出价弹窗，取本人上一票相同货源成交价，srcMsgId:{}", srcMsgId);
        TransportMainDO lastSameTransport = transportMainService.getLastSameDealTransport(transport);
        if (lastSameTransport != null) {
            priceBoxDTO.setShowDialogType(1);
            priceBoxDTO.setPrice(new BigDecimal(lastSameTransport.getPrice()).intValue());
            priceBoxDTO.setPrompt(promptProperties.getPriceBoxPrompt(1, priceBoxDTO.getPrice(), null));
        } else {
            // 优先级2:优先取本人上一票相似货源成交价（相似货源维度同已有功能）
            log.info("直接发布是否弹出出价弹窗，取相似货源成交价，srcMsgId:{}", srcMsgId);
            SameTransportAvgPriceResultDTO sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPrice(srcMsgId, userId);
            if (sameTransportAvgPrice != null && sameTransportAvgPrice.getLastPrice() > 0) {
                priceBoxDTO.setShowDialogType(1);
                priceBoxDTO.setPrice(sameTransportAvgPrice.getLastPrice());
                priceBoxDTO.setPrompt(promptProperties.getPriceBoxPrompt(2, priceBoxDTO.getPrice(), null));
                // 优先级3:取大盘相似货源成交平均价（相似货源维度同已有功能）
            } else if (sameTransportAvgPrice != null) {
                priceBoxDTO.setShowDialogType(1);
                priceBoxDTO.setPrice(sameTransportAvgPrice.getAveragePrice().intValue());
                priceBoxDTO.setPrompt(promptProperties.getPriceBoxPrompt(3, priceBoxDTO.getPrice(), sameTransportAvgPrice.getSameCount()));
            }
        }
        log.info("直接发布是否弹出出价弹窗，设置价格结束，srcMsgId:{}", srcMsgId);

        // 专票货源返回费率
        if (priceBoxDTO.getShowDialogType() == 1 && Objects.equals(transport.getInvoiceTransport(), 1)) {
            log.info("直接发布是否弹出出价弹窗，设置专票费率开始，srcMsgId:{}", srcMsgId);
            priceBoxDTO.setInvoiceTransport(1);
            priceBoxDTO.setEnterpriseTaxRate(DEFAULT_ENTERPRISE_TAX_RATE);
            TransportEnterpriseLogDO transportEnterpriseLogDO = transportEnterpriseLogService.getBySrcMsgId(srcMsgId);
            if (transportEnterpriseLogDO != null) {
                Long invoiceSubjectId = transportEnterpriseLogDO.getInvoiceSubjectId();
                try {
                    log.info("直接发布是否弹出出价弹窗，调用费率接口开始，userId:{},invoiceSubjectId:{}", userId, invoiceSubjectId);
                    WebResult<BigDecimal> webResult = thirdEnterpriseRemoteService.getTaxRate(invoiceSubjectId, userId);
                    if (webResult != null && webResult.ok()) {
                        priceBoxDTO.setEnterpriseTaxRate(webResult.getData());
                    }
                    log.info("直接发布是否弹出出价弹窗，调用费率接口结束，webResult:{}", JSON.toJSONString(webResult));
                } catch (Exception e) {
                    log.error("直接发布是否弹出出价弹窗，调用费率接口异常，srcMsgId:{}，userId:{}, invoiceSubjectId:{}", srcMsgId, userId, invoiceSubjectId);
                }
            }
        }

        return priceBoxDTO;
    }

    /**
     * 直接发布时是否展示升级为优车发货弹窗
     *
     * @param srcMsgId
     * @return
     */
    @Override
    public UpgradeExcellentDTO showExcellentPricePopup(Long srcMsgId) {
        UpgradeExcellentDTO upgradeExcellentDTO = new UpgradeExcellentDTO();
        TransportMainDO mainDO = transportMainService.getById(srcMsgId);

        // 有价普通货源判断弹窗是否展示
        if (Objects.isNull(mainDO) || !Objects.equals(mainDO.getExcellentGoods(), ExcellentGoodsEnums.NORMAL.getCode())
                || StringUtils.isBlank(mainDO.getPrice()) || StringUtils.equals(mainDO.getPrice(), "0")) {
            return upgradeExcellentDTO;
        }

        TransportCarryDTO carryDTO = buildTransportCarryDTO(mainDO);
        GoodCarPriceTransportTabAndBIPriceDTO showGoodCarPriceTransportTab = goodCarPriceTransportService.isShowGoodCarPriceTransportTab(carryDTO);
        if (Objects.isNull(showGoodCarPriceTransportTab) || !showGoodCarPriceTransportTab.getShowTab()) {
            return upgradeExcellentDTO;
        }
        upgradeExcellentDTO.setShowPopup(true);
        upgradeExcellentDTO.setTitle("免费升级为优车发货");
        upgradeExcellentDTO.setPrompt("预计X分钟接单".replace("X", transportAfterOrderDataService.getExcellentPricePopupPrompt(mainDO).toString()));
        Integer publishType = PublishTypeEnum.FIXED.getCode();
        // 6680逻辑调整，只支持一口价
        /*if (Objects.equals(showGoodCarPriceTransportTab.getIsRouteSupportTeleNegotiation(), 1) &&
                Objects.equals(showGoodCarPriceTransportTab.getIsAllowTeleNegotiation(), 1)) {
            publishType = mainDO.getPublishType();
        }*/
        upgradeExcellentDTO.setPublishType(publishType);
        upgradeExcellentDTO.setThMinPrice(showGoodCarPriceTransportTab.getThMinPrice());
        upgradeExcellentDTO.setThMaxPrice(showGoodCarPriceTransportTab.getThMaxPrice());
        upgradeExcellentDTO.setFixPriceFast(showGoodCarPriceTransportTab.getFixPriceFast());
        upgradeExcellentDTO.setFixPriceMin(showGoodCarPriceTransportTab.getFixPriceMin());
        upgradeExcellentDTO.setFixPriceMax(showGoodCarPriceTransportTab.getFixPriceMax());

        return upgradeExcellentDTO;
    }

    private TransportCarryDTO buildTransportCarryDTO(TransportMainDO mainDO) {
        TransportCarryDTO transportCarryDTO = new TransportCarryDTO();
        transportCarryDTO.setStartProvince(mainDO.getStartProvinc());
        transportCarryDTO.setStartCity(mainDO.getStartCity());
        transportCarryDTO.setStartArea(mainDO.getStartArea());
        transportCarryDTO.setDestProvince(mainDO.getDestProvinc());
        transportCarryDTO.setDestCity(mainDO.getDestCity());
        transportCarryDTO.setDestArea(mainDO.getDestArea());
        transportCarryDTO.setGoodsWeight(mainDO.getWeight());
        transportCarryDTO.setGoodsLength(mainDO.getLength());
        transportCarryDTO.setGoodsWide(mainDO.getWide());
        transportCarryDTO.setGoodsHigh(mainDO.getHigh());
        transportCarryDTO.setUserId(mainDO.getUserId());
        transportCarryDTO.setDistance(mainDO.getDistance() != null ? mainDO.getDistance().toString() : "0");
        transportCarryDTO.setGoodTypeName(mainDO.getGoodTypeName());
        return transportCarryDTO;
    }

    /**
     * 我的货源页点击撤销是否弹出挽留弹窗接口
     */
    @Override
    public ShowRetentionDTO showRetention(Long srcMsgId) {
        ShowRetentionDTO showRetentionDTO = new ShowRetentionDTO();
        showRetentionDTO.setShowDialogType(1);
        showRetentionDTO.setSrcMsgId(srcMsgId);

        TransportMainDO transport = transportMainService.getTransportMainForId(srcMsgId);
        // 货源不在发布中，不显示
        if (transport == null || transport.getStatus() != 1) {
            log.warn("点击撤销是否弹出挽留弹窗，货源不是发布中，srcMsgId:{}", srcMsgId);
            return showRetentionDTO;
        }

        boolean hasPrice = TransportUtil.hasPrice(transport.getPrice());
        showRetentionDTO.setHasPrice(hasPrice ? 1 : 0);
        List<TransportAfterOrderDataDO> sameTransportList = transportAfterOrderDataService.getSameTransport(srcMsgId, null, false, 7);

        if (CollectionUtils.isEmpty(sameTransportList)) {
            showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, hasPrice, null));
        } else {
            boolean defaultRule = true;

            // 有价先判断近7天相似货源成交价高于货源价格
            if (hasPrice) {
                double avgPrice = sameTransportList.stream().map(TransportAfterOrderDataDO::getPrice).filter(StringUtils::isNotBlank).mapToInt(Integer::parseInt).average().orElse(0);
                BigDecimal dealPrice = new BigDecimal(avgPrice).divide(FIFTY, 0, RoundingMode.UP).multiply(FIFTY);
                if (dealPrice.intValue() > Integer.parseInt(transport.getPrice())) {
                    defaultRule = false;
                    TransportMainExtendDO mainExtendDO = transportMainExtendService.getBySrcMsgId(srcMsgId);
                    boolean isCarpool = Objects.nonNull(mainExtendDO) && Objects.equals(UseCarTypeEnum.PART.getCode(), mainExtendDO.getUseCarType());
                    if (!isCarpool) {
                        showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, true, dealPrice.intValue()));
                    }
                }
            }

            // 如果低于，或者无价货源，判断近7天相似货源成交时长中位数 - 等待时间
            if (defaultRule) {
                // 获取近7天所有相似货源成交时长中位数，单位毫秒
                long dealMedianDuration = getDealMedianDuration(sameTransportList);
                // 当前货源已发布时长
                long curTransportDuration = System.currentTimeMillis() - transport.getPubDate().getTime();
                // 等待时间超过90分钟
                long waitTime = (dealMedianDuration - curTransportDuration) / 1000 / 60;
                if (waitTime >= 90) {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, false, 90));
                } else if (waitTime >= 30) {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, false, (int) waitTime));
                } else {
                    showRetentionDTO.setTitle(promptProperties.getRetentionPrompt(1, hasPrice, null));
                }
            }
        }

        // 设置任务区，无价展示填价，有价展示加价
        List<ShowRetentionDTO.ShowRetentionTaskDTO> taskList = new ArrayList<>();
        ShowRetentionDTO.ShowRetentionTaskDTO task = new ShowRetentionDTO.ShowRetentionTaskDTO();
        task.setType(!hasPrice ? 1 : 2);
        task.setPrompt(promptProperties.getRetentionPrompt(2, hasPrice, null));
        taskList.add(task);
        // 撤销并发布选项
        ShowRetentionDTO.ShowRetentionTaskDTO cancelTask = new ShowRetentionDTO.ShowRetentionTaskDTO();
        cancelTask.setType(4);
        cancelTask.setPrompt(promptProperties.getRetentionRepublish());
        taskList.add(cancelTask);
        showRetentionDTO.setTaskList(taskList);

        return showRetentionDTO;
    }

    /**
     * 点击货源发布弹出出价弹窗接口
     *
     * @param TransportPublishDTO
     */
    @Override
    public PopUpPriceBoxDTO publishPopupPriceBox(TransportPublishDTO TransportPublishDTO) {
        log.info("点击货源发布弹出出价弹窗接口，入参，TransportPublishDTO:{}", JSON.toJSONString(TransportPublishDTO));
        PopUpPriceBoxDTO priceBoxDTO = new PopUpPriceBoxDTO();

        // 获取相似货源数量，返回不同文案
        TransportMainDO mainDO = TransportPublishConverter.INSTANCE.toMainDO(TransportPublishDTO);
        String similarityCode = transportMainService.genSimilarityCode(mainDO);
        int similarityCount = transportMainService.countSimilarityGoods(similarityCode, null);
        if (similarityCount == 0) {
            priceBoxDTO.setTitle(promptProperties.getPublishPriceBoxPrompt("title-no-similar", null, null));
        } else {
            priceBoxDTO.setTitle(promptProperties.getPublishPriceBoxPrompt("title-has-similar", String.valueOf(similarityCount), null));
        }
        priceBoxDTO.setContent(promptProperties.getPublishPriceBoxPrompt("content", null, null));

        // 有相似货源成交价，优先显示
        SameTransportAvgPriceQueryDTO priceQueryDTO = new SameTransportAvgPriceQueryDTO();
        priceQueryDTO.setStartCity(TransportPublishDTO.getStartCity());
        priceQueryDTO.setDestCity(TransportPublishDTO.getDestCity());
        priceQueryDTO.setDistance(TransportPublishDTO.getDistance() == null ? BigDecimal.ZERO : TransportPublishDTO.getDistance());
        priceQueryDTO.setGoodsWeight(TransportPublishDTO.getWeight());
        priceQueryDTO.setGoodTypeName(TransportPublishDTO.getGoodTypeName());
        priceQueryDTO.setHasPrice(true);
        SameTransportAvgPriceResultDTO sameTransportAvgPrice = transportAfterOrderDataService.getSameTransportAvgPrice(priceQueryDTO);
        if (sameTransportAvgPrice != null) {
            priceBoxDTO.setPrice(sameTransportAvgPrice.getAveragePrice().intValue());
            priceBoxDTO.setPrompt(promptProperties.getPublishPriceBoxPrompt("prompt", sameTransportAvgPrice.getDayNum(), sameTransportAvgPrice.getAveragePrice().toString()));
        } else {
            // 若未成功获取相似货源价格，则取优车2.0最低值展示，天数固定为7
            TransportCarryReq carryReq = TransportPublishConverter.INSTANCE.toCarryReq(TransportPublishDTO);
            carryReq.setUserId(LoginHelper.getLoginUser().getUserId());
            CarryPriceVO thPrice = thPriceService.getThPrice(carryReq);
            if (thPrice != null && thPrice.getFixPriceMin() != null) {
                priceBoxDTO.setPrice(thPrice.getFixPriceMin());
                priceBoxDTO.setPrompt(promptProperties.getPublishPriceBoxPrompt("prompt", "7", String.valueOf(thPrice.getFixPriceMin())));
            }
        }

        log.info("点击货源发布弹出出价弹窗接口，出参，priceBoxDTO:{}", JSON.toJSONString(priceBoxDTO));
        return priceBoxDTO;
    }

    /**
     * 获取近7天所有相似货源成交时长中位数，返回秒数
     */
    private long getDealMedianDuration(List<TransportAfterOrderDataDO> sameTransportList) {
        // 查询7天成交货源的id，发布时间
        List<Long> srcMsgIds = sameTransportList.stream().map(TransportAfterOrderDataDO::getSrcMsgId).toList();
        QueryWrapper<TransportMainDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "pub_date");
        queryWrapper.in("id", srcMsgIds);
        List<TransportMainDO> transportMainList = transportMainService.getBaseMapper().selectList(queryWrapper);

        // 获取成交时长
        List<Long> dealDuration = new ArrayList<>();
        Map<Long, Date> collect = transportMainList.stream().collect(Collectors.toMap(TransportMainDO::getId, TransportMainDO::getPubDate));
        for (TransportAfterOrderDataDO sameTransport : sameTransportList) {
            Date pubDate = collect.get(sameTransport.getSrcMsgId());
            if (pubDate != null) {
                dealDuration.add(sameTransport.getCreateTime().getTime() - pubDate.getTime());
            }
        }

        // 计算成交时长中位数
        dealDuration.sort(Comparator.comparingLong(t -> t));
        int length = dealDuration.size();
        long median;
        if (length % 2 == 0) {
            // 偶数个元素，取中间两个数的平均值
            median = (dealDuration.get(length / 2 - 1) + dealDuration.get(length / 2)) / 2;
        } else {
            // 奇数个元素，取中间的数
            median = dealDuration.get(length / 2);
        }
        return median;
    }

    /**
     * 货源发布、变更时弹窗埋点记录接口
     */
    @Override
    public void popupTracking(PopupTrackingLogDTO trackingLog) {
        transportPopupTrackingLogService.save(trackingLog);
    }

    @Override
    public PublishTransportTypeDataResultDTO getPublishTransportTypeData(PublishTransportTypeDataDTO dataDTO) {
        return transportPublishService.getPublishTransportTypeData(dataDTO);
    }
}

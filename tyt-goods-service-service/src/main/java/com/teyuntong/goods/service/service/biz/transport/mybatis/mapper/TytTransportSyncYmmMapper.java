package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.DispatchCompanyDO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportSyncYmmDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TytTransportSyncYmmMapper extends BaseMapper<TransportSyncYmmDO> {

    TransportSyncYmmDO selectTytTransportSyncYmmBySrcId(@Param("srcMsgId") Long srcMsgId);

    TransportSyncYmmDO getByCargoId(Long cargoId);

}
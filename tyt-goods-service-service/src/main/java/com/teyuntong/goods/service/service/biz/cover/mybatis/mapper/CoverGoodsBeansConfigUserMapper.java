package com.teyuntong.goods.service.service.biz.cover.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.cover.dto.CoverGoodsUserBeansDTO;
import com.teyuntong.goods.service.service.biz.cover.mybatis.entity.CoverGoodsBeansConfigUserDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 抢单豆规则配置用户导入表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-25
 */
@Mapper
public interface CoverGoodsBeansConfigUserMapper extends BaseMapper<CoverGoodsBeansConfigUserDO> {

    /**
     * 根据userId获取当前用户剩余抢单豆总个数
     * @param userId
     * @return int
     */
    CoverGoodsUserBeansDTO selectTotalBeansLeftNumByUserId(@Param("userId") Long userId);
}

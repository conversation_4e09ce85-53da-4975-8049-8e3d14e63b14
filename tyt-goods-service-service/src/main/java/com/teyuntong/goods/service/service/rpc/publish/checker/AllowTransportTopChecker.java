package com.teyuntong.goods.service.service.rpc.publish.checker;

import cn.hutool.core.date.DateUtil;
import com.teyuntong.goods.service.client.publish.dto.DirectPublishDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import com.teyuntong.goods.service.service.biz.transport.service.TransportService;
import com.teyuntong.goods.service.service.common.error.GoodsErrorCode;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserPermissionRemoteService;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishBO;
import com.teyuntong.goods.service.service.rpc.publish.bo.DirectPublishProcessBO;
import com.teyuntong.infra.common.definition.exception.BusinessException;
import com.teyuntong.user.service.client.permission.dto.AuthPermissionRpcDTO;
import com.teyuntong.user.service.client.permission.enums.ServicePermissionEnum;
import com.teyuntong.user.service.client.permission.vo.AuthPermissionRpcVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 是否允许置顶checker
 *
 * <AUTHOR>
 * @since 2025/02/23 15:52
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AllowTransportTopChecker {

    private final UserPermissionRemoteService userPermissionRemoteService;
    private final TytConfigRemoteService tytConfigRemoteService;
    private final TransportService transportService;

    /**
     * 直接发布校验
     */
    public void checkDirectPublish(DirectPublishProcessBO processBO) {
        DirectPublishBO directPublishBO = processBO.getDirectPublishBO();
        if (!processBO.getDirectPublishBO().isHistoryGoods()) {
            if (directPublishBO.isTopFlag() && directPublishBO.isUseExposure()) {

                Long userId = processBO.getUser().getId();
                // 校验权益(产品强制要求先判断权益)
                this.checkRefreshPermission(userId, false);

                // 校验货源本身刷新条件
                this.checkTransportRefresh(directPublishBO.getSrcMsgId());

                //校验并扣除权益
                this.checkRefreshPermission(userId, true);
            }
        }
    }

    /**
     * 单独校验用户是否有置顶权益
     */
    private void checkRefreshPermission(Long userId, boolean usePermission) {

        AuthPermissionRpcDTO authPermissionRpcDTO = new AuthPermissionRpcDTO();
        authPermissionRpcDTO.setServicePermissionEnum(ServicePermissionEnum.货源曝光权益);
        authPermissionRpcDTO.setUserId(userId);

        AuthPermissionRpcVO authPermissionRpcVO;
        if (usePermission) {
            // 直接扣除
            authPermissionRpcVO = userPermissionRemoteService.authPermission(authPermissionRpcDTO);
        } else {
            // 只校验权益
            authPermissionRpcVO = userPermissionRemoteService.checkAuthPermission(authPermissionRpcDTO);
        }

        if (!authPermissionRpcVO.isUse()) {
            log.error("user_transport_top_permission_not_available, userId : {}", userId);
            throw new BusinessException(GoodsErrorCode.TRANSPORT_TOP_USE_UP);
        }
    }

    /**
     * 校验货源刷新次数和频率
     */
    private void checkTransportRefresh(Long srcMsgId) {
        // 1. 校验刷新时间间隔
        Integer topInterval = tytConfigRemoteService.getIntValue("tyt:plat:config:transport_top_interval", 10 * 60);
        // 2. 校验最大刷新次数
        Integer maxResendCounts = tytConfigRemoteService.getIntValue("tyt:plat:config:transport_top_max_count", 16);

        TransportDO lastTransport = transportService.getLastBySrcMygId(srcMsgId);

        // 1. 校验刷新时间间隔
        if (maxResendCounts > 0 && DateUtil.betweenMs(lastTransport.getCtime(), new Date()) < topInterval * 1000L) {
            throw new BusinessException(GoodsErrorCode.TRANSPORT_TOP_LIMIT_TIME);
        }

        // 2. 校验最大刷新次数
        if (lastTransport.getResendCounts() >= maxResendCounts) {
            throw new BusinessException(GoodsErrorCode.TRANSPORT_MAX_TOP);
        }
    }
}

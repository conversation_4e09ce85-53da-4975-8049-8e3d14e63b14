package com.teyuntong.goods.service.service.biz.publish.mybatis.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.publish.mybatis.entity.TransportAutoResendUserDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 * 货源自动重发用户表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Mapper
@DS("tyt")
public interface TransportAutoResendUserMapper extends BaseMapper<TransportAutoResendUserDO> {

    TransportAutoResendUserDO getByUserId(Long userId);
}

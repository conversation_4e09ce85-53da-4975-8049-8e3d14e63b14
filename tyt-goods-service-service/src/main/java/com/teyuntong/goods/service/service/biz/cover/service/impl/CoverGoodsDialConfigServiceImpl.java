package com.teyuntong.goods.service.service.biz.cover.service.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.teyuntong.goods.service.client.transport.vo.GoodsSingleDetailResultVO;
import com.teyuntong.goods.service.service.biz.callphonerecord.bean.TransportLabelJson;
import com.teyuntong.goods.service.service.biz.callphonerecord.service.TransportViewLogService;
import com.teyuntong.goods.service.service.biz.cover.dto.CoverGoodsDialInfoDTO;
import com.teyuntong.goods.service.service.biz.cover.dto.CoverGoodsUserBeansDTO;
import com.teyuntong.goods.service.service.biz.cover.mybatis.entity.CoverGoodsDialConfigDO;
import com.teyuntong.goods.service.service.biz.cover.mybatis.entity.CoverGoodsDialConfigUserDO;
import com.teyuntong.goods.service.service.biz.cover.mybatis.entity.CoverGoodsLogDO;
import com.teyuntong.goods.service.service.biz.cover.mybatis.mapper.CoverGoodsDialConfigMapper;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsBeansConfigUserService;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsDialConfigService;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsDialConfigUserService;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsDialUserUseLogService;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsLogService;
import com.teyuntong.goods.service.service.biz.cover.service.CoverGoodsWhiteListConfigUserService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportMainDO;
import com.teyuntong.goods.service.service.remote.basic.TytConfigRemoteService;
import com.teyuntong.goods.service.service.remote.user.UserRemoteService;
import com.teyuntong.infra.common.redis.utils.RedisUtil;
import com.teyuntong.user.service.client.user.vo.ApiDataUserCreditInfoRpcVO;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.Serializable;
import java.time.Duration;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * 捂货规则配置表 服务实现类
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-25
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class CoverGoodsDialConfigServiceImpl implements CoverGoodsDialConfigService {
    private final CoverGoodsDialConfigMapper coverGoodsDialConfigMapper;
    private final CoverGoodsDialConfigUserService coverGoodsDialConfigUserService;
    private final CoverGoodsWhiteListConfigUserService coverGoodsWhiteListConfigUserService;
    private final CoverGoodsDialUserUseLogService coverGoodsDialUserUseLogService;
    private final CoverGoodsBeansConfigUserService coverGoodsBeansConfigUserService;
    private final RedisUtil redisUtil;
    private final UserRemoteService userRemoteService;
    private final TransportViewLogService transportViewLogService;
    private final CoverGoodsLogService coverGoodsLogService;
    private final TytConfigRemoteService tytConfigRemoteService;

    private static final String DIAL_INFO_REDIS_KEY = "dial:info:userId:{%d}:tsId:{%d}";

    /**
     * 6500 捂货需求4.0
     * <pre>
     * 0. 判断是否勾选优推好车主，不勾选跳过，
     * 1. 不在捂货名单中且不在捂货白名单中，不捂货，跳过
     * 2. 高等级车主不捂货，如果车主lv>=5，直接不捂货，跳过
     * 3. 用过免责卡，不捂货，跳过
     * 4. 没走好货模型的货源不参与捂货，如代调发货、专车货源
     * 5. 每天第一次点击不捂货，只针对中货、差货
     * 7. 判断捂货记录表里是否存在该货源，存在就返回捂货信息，里面有捂货结束时间，判断是否过期
     * 8. 已浏览过的货源不捂货
     * 9. 最后根据货源级别：好货|中货|差货判断
     *      a. 好货100%捂货
     *     	a. 判断捂货次数是否 < N，大于则不捂货（好货不限制）
     *     	b. 根据缓存中浏览过的中货|差货的次数，判断是否是配置的倍数，是则捂货，不是则不捂货。
     * 10.命中捂货规则，记录到捂货记录表中
     *
     * 注意：
     * 1. 已经点过的货源不计入次数
     * 2. 已经捂过的货不能再次被捂
     * 3. 中货差货区分开逻辑
     * 4. 每天每人最多捂货N，好货不计入N
     * 5. 每天第一次点击不捂货，后面开始隔X捂货
     *
     * APP端弹出捂货弹框的条件：
     * 1. 货源priorityRecommendExpireTime != null
     * 2. 货源status == 1
     * 3. 捂货expire == false
     * 4. dialUser == true
     * 5. useN == false
     * 如果用户在捂货名单中，才会弹倒计时；只在捂货白名单中，会弹捂货提示语，不需要倒计时。
     * </pre>
     */
    @Override
    public GoodsSingleDetailResultVO.CoverGoodsDialInfo coverGoodsVersion4(Long userId, TransportMainDO main) {
        try {
            Long srcMsgId = main.getSrcMsgId();

            GoodsSingleDetailResultVO.CoverGoodsDialInfo coverGoodsDialInfo = buildCoverGoodsDialInfo(userId, srcMsgId);
            coverGoodsDialInfo.setExpire(true); // 先设置默认过期，不弹窗
            // log.info("旧逻辑查询的捂货信息：{}", JSON.toJSONString(coverGoodsDialInfo));

            // 0. 是否勾选优推好车主
            if (!isPriorityRecommend(srcMsgId)) {
                log.info("货主没有勾选优推好车主，不捂货，srcMsgId:{}", srcMsgId);
                return coverGoodsDialInfo;
            }

            // 1. 没有捂货到期时间，不捂货
            if (main.getPriorityRecommendExpireTime() == null) {
                log.info("货源没有捂货过期时间，不捂货，srcMsgId:{}", srcMsgId);
                return coverGoodsDialInfo;
            }

            // 1. 不在捂货名单和捂货白名单中，不捂货
            if (!coverGoodsDialInfo.isDialUser()) {
                log.info("当前用户不在捂货名单和捂货白名单中，不捂货：{}", userId);
                return coverGoodsDialInfo;
            }

            // 3. 用过免责卡，不捂货
            if (coverGoodsDialInfo.getUseN()) {
                log.info("车主已使用过免责卡，不捂货：userId: {}, srcMsgId: {}", userId, srcMsgId);
                return coverGoodsDialInfo;
            }

            // 4. 如果没走好货模型，忽略
            TransportLabelJson transportLabelJson = JSON.parseObject(main.getLabelJson(), TransportLabelJson.class);
            if (transportLabelJson == null || transportLabelJson.getIGBIResultData() == null) {
                log.info("该货源没走好货模型，iGBIResultData是空：{}", main.getLabelJson());
                return coverGoodsDialInfo;
            }

            // 2. 高等级车主不捂货
            ApiDataUserCreditInfoRpcVO userCreditInfo = userRemoteService.getUserCreditInfo(userId);
            if (userCreditInfo != null) {
                String carCreditRankLevel = userCreditInfo.getCarCreditRankLevel();
                if (StringUtils.isNotBlank(carCreditRankLevel) && Integer.parseInt(carCreditRankLevel) >= 5) {
                    log.info("当前用户的信用等级>=5，不捂货：{}", userId);
                    return coverGoodsDialInfo;
                }
            }

            // 6. 每天第一次点击不捂货，只对中货差货生效
            Integer goodsLevel = transportLabelJson.getIGBIResultData();
            String goodsViewKey = getGoodsViewKey(userId);
            Map<String, Object> goodsViewMap = getGoodsViewMapAndInit(goodsViewKey, goodsLevel);
            if (goodsViewMap.isEmpty() && goodsLevel != 1) {
                log.info("每天第一次点击不捂货");
                return coverGoodsDialInfo;
            }

            // 7. 如果倒计时未结束，返回捂货信息
            if (coverGoodsDialInfo.getLeftYSeconds() != null && coverGoodsDialInfo.getLeftYSeconds() > 0) {
                coverGoodsDialInfo.setExpire(main.getPriorityRecommendExpireTime().before(new Date()));
                log.info("该货源之前点击过，是否需要捂货，需要判断是否过期：{}", JSON.toJSONString(coverGoodsDialInfo));
                return coverGoodsDialInfo;
            }

            // 8. 已浏览过不再捂货
            boolean viewed = transportViewLogService.isViewed(userId, main.getSrcMsgId());
            if (viewed) {
                log.info("该货源已浏览过，不再捂货：{}，表tyt_transport_view_log", main.getSrcMsgId());
                return coverGoodsDialInfo;
            }

            // 浏览次数+1
            int viewTimes = 0;
            Object obj = goodsViewMap.get(goodsLevel.toString());
            if (Objects.nonNull(obj)) {
                viewTimes = (int) obj;
            }
            viewTimes = viewTimes + 1;
            // 缓存到今天24:00点失效
            Date now = new Date();
            long cacheSeconds = (DateUtil.endOfDay(now).getTime() - now.getTime()) / 1000 + 1000;
            redisUtil.hashPut(goodsViewKey, goodsLevel.toString(), viewTimes, Duration.ofSeconds(cacheSeconds));

            // 9. 根据货源等级，分别判断是否捂货
            int coverGoodsInterval = isCoverGoods(goodsLevel, userId, viewTimes);
            if (coverGoodsInterval == 0) {
                log.info("货源等级：{}，当前用户浏览次数：{}，不捂货，srcMsgId:{}", goodsLevel, viewTimes, srcMsgId);
                return coverGoodsDialInfo;
            }

            // 要捂货，判断是否过了捂货时间
            boolean expire = main.getPriorityRecommendExpireTime().before(new Date());
            coverGoodsDialInfo.setExpire(expire);

            // 保存捂货记录
            if (!expire) {
                coverGoodsDialInfo.setLeftYSeconds(coverGoodsDialInfo.getConfigYSeconds());
                saveCoverGoodsLog(userId, main, coverGoodsDialInfo, goodsLevel, viewTimes, coverGoodsInterval);
                log.info("该货源需要捂货，新的捂货信息为：{}", JSON.toJSONString(coverGoodsDialInfo));
            } else {
                log.info("该货源已过期，不需要捂货：{}", JSON.toJSONString(coverGoodsDialInfo));
            }

            return coverGoodsDialInfo;
        } catch (Exception e) {
            log.error("getSingleDetail货源详情 coverGoodsVersion4 error, userId:{}, srcMsgId:{}", userId, main.getId(), e);
        }
        return null;
    }

    /**
     * 保存捂货记录
     */
    private void saveCoverGoodsLog(Long userId, TransportMainDO transportMain,
                                   GoodsSingleDetailResultVO.CoverGoodsDialInfo coverGoodsDialInfo,
                                   Integer goodsLevel, int viewTimes,int coverGoodsInterval) {
        // 保存捂货记录
        CoverGoodsLogDO coverGoodsLog = new CoverGoodsLogDO();
        coverGoodsLog.setUserId(userId);
        coverGoodsLog.setTsId(transportMain.getSrcMsgId());
        coverGoodsLog.setGoodsLevel(goodsLevel);
        coverGoodsLog.setViewIndex(viewTimes);
        String hitRule = goodsLevel == 1 ? "好货100%捂货" : "中货差货间隔捂货";
        coverGoodsLog.setHitRule(hitRule);
        coverGoodsLog.setPriorityRecommendExpireTime(transportMain.getPriorityRecommendExpireTime());
        coverGoodsLog.setXTimeInSeconds(coverGoodsDialInfo.getConfigXSeconds());
        coverGoodsLog.setYTimeInSeconds(coverGoodsDialInfo.getConfigYSeconds());
        coverGoodsLog.setCoverInterval(coverGoodsInterval);
        coverGoodsLog.setCreateTime(new Date());
        coverGoodsLogService.saveCoverGoodsLog(coverGoodsLog);
    }

    /**
     * 按货物等级判断是否捂货:好货直接捂货；中货差货按间隔捂
     * @return 返回捂货间隔，如果=0不捂货；>0都捂货
     */
    private int isCoverGoods(Integer goodsLevel, Long userId, Integer viewTimes) {
        if (goodsLevel == 1) {
            log.info("好货100%捂货");
            return 1;
        }
        // 获取当天已捂货次数
        int todayCoverTimes = coverGoodsLogService.countTodayCoverTimesWithoutGoodGoods(userId);

        String coverGoodsConfig = tytConfigRemoteService.getStringValue("cover_goods_config", "{}");
        JSONObject coverGoodsMap = JSONObject.parseObject(coverGoodsConfig);
        int maxTimesEveryDay = coverGoodsMap.getIntValue("N");  // 每天最大捂货次数

        // 超过最大次数，不捂货
        if (todayCoverTimes >= maxTimesEveryDay) {
            log.info("今天捂货次数已超过最大捂货次数，不再捂货，今天已捂货：{}，最大捂货次数：{}", todayCoverTimes, maxTimesEveryDay);
            return 0;
        }

        // 只有是间隔的倍数才捂货
        int goodsInterval = coverGoodsMap.getIntValue(goodsLevel.toString());
        if (goodsInterval == 0) {
            log.info("没有配置当前货源类型的捂货次数，不捂货");
            return 0;
        }
        log.info("这个货源的等级是{}，浏览次数是：{}，捂货倍数是：{}，是否捂货：{}", goodsLevel, viewTimes, goodsInterval, viewTimes % goodsInterval == 0);
        return viewTimes % goodsInterval == 0 ? goodsInterval : 0;
    }

    /**
     * 返回缓存中的浏览记录
     * {"1":100,"2":100,"3":100} key是货物等级，value是浏览次数
     */
    private Map<String, Object> getGoodsViewMapAndInit(String goodsViewKey, Integer goodsLevel) {
        Date now = new Date();
        Map<String, Object> goodsViewMap = redisUtil.hashEntries(goodsViewKey);
        if (goodsViewMap == null) {
            goodsViewMap = new HashMap<>();
            // 缓存到今天24:00点失效
            long cacheSeconds = (DateUtil.endOfDay(now).getTime() - now.getTime()) / 1000 + 1000;
            redisUtil.hashPut(goodsViewKey, goodsLevel.toString(), 1, Duration.ofSeconds(cacheSeconds));
        }
        return goodsViewMap;
    }

    private String getGoodsViewKey(Long userId) {
        return this.joinRedisKey("tyt:goods:view", DateUtil.format(new Date(), "yyyyMMdd"), userId.toString());
    }

    /**
     * 是否勾选优推好车主
     */
    private boolean isPriorityRecommend(Long srcMsgId) {
        // String today = DateUtil.format(new Date(), "yyyyMMdd");
        // String biDataKey = this.joinRedisKey("tyt:cache:bi:data:", srcMsgId.toString(),  today);
        // String biData = redisUtil.getString(biDataKey);
        // JSONObject transportBIDataJson = JSONObject.parseObject(biData);
        // return transportBIDataJson != null && Objects.equals(transportBIDataJson.getInteger("priorityRecommend"), 1);
        return false;
    }

    /**
     * 构建捂货信息，这是之前的逻辑
     */
    private GoodsSingleDetailResultVO.CoverGoodsDialInfo buildCoverGoodsDialInfo(Long userId, Long srcMsgId) {
        GoodsSingleDetailResultVO.CoverGoodsDialInfo coverGoodsDialInfo = new GoodsSingleDetailResultVO.CoverGoodsDialInfo();

        CoverGoodsDialInfoDTO coverGoodsDialInfoVO = this.getCoverGoodsDialInfo(userId);
        coverGoodsDialInfo.setDialUser(coverGoodsDialInfoVO.isDialUser());
        coverGoodsDialInfo.setDialWhiteUser(coverGoodsDialInfoVO.isDialWhiteUser());
        coverGoodsDialInfo.setLeftN(coverGoodsDialInfoVO.getLeftN());
        coverGoodsDialInfo.setConfigXSeconds(coverGoodsDialInfoVO.getConfigXSeconds());
        coverGoodsDialInfo.setConfigYSeconds(coverGoodsDialInfoVO.getConfigYSeconds());

        UserTsDialInfoRedisBean userTsDialInfoBean = redisUtil.getBean(getUserTsDialInfoRedisKey(userId, srcMsgId), UserTsDialInfoRedisBean.class);
        if (userTsDialInfoBean == null) {
            coverGoodsDialInfo.setUseN(false);
            coverGoodsDialInfo.setCountDownFinished(false);
        } else {
            coverGoodsDialInfo.setUseN(userTsDialInfoBean.getUseN());
            coverGoodsDialInfo.setLeftYSeconds(userTsDialInfoBean.getLeftYSeconds());
            coverGoodsDialInfo.setCountDownFinished(userTsDialInfoBean.isCountDownFinished());
        }
        return coverGoodsDialInfo;
    }

    private String getUserTsDialInfoRedisKey(Long userId, Long tsId) {
        return String.format(DIAL_INFO_REDIS_KEY, userId, tsId);
    }

    private CoverGoodsDialInfoDTO getCoverGoodsDialInfo(Long userId) {
        CoverGoodsDialInfoDTO coverGoodsDialInfoVO = new CoverGoodsDialInfoDTO();
        CoverGoodsDialConfigUserDO configUser = coverGoodsDialConfigUserService.selectEnabledLastImported(userId);
        if (configUser != null) {
            CoverGoodsDialConfigDO tytCoverGoodsDialConfig =
                    coverGoodsDialConfigMapper.selectById(configUser.getDialConfigId());
            coverGoodsDialInfoVO.setDialUser(true);
            coverGoodsDialInfoVO.setConfigXSeconds(tytCoverGoodsDialConfig.getXTimeInSeconds());
            coverGoodsDialInfoVO.setConfigYSeconds(tytCoverGoodsDialConfig.getYTimeInSeconds());
        } else {
            coverGoodsDialInfoVO.setDialUser(false);
            Integer configMaxXSeconds = selectXTime();
            coverGoodsDialInfoVO.setConfigMaxXSeconds(configMaxXSeconds);
            coverGoodsDialInfoVO.setConfigXSeconds(configMaxXSeconds);
            // 如果不在配置名单内，就不倒计时
            coverGoodsDialInfoVO.setConfigYSeconds(0);
        }
        coverGoodsDialInfoVO.setLeftN(getDialConfigLeftN(userId));
        //查看用户是否在捂货白名单内
        Long whiteUser = coverGoodsWhiteListConfigUserService.countByUserId(userId);
        if (null != whiteUser && whiteUser.intValue() > 0) {
            coverGoodsDialInfoVO.setDialWhiteUser(true);
        } else {
            coverGoodsDialInfoVO.setDialWhiteUser(false);
        }

        coverGoodsDialInfoVO.setGainTimes(coverGoodsDialUserUseLogService.countByUserIdAndChangeType(userId, 1));
        return coverGoodsDialInfoVO;
    }

    /**
     * 从数据库中获取用户抢单豆剩余总次数
     *
     * @param userId userId
     * @return 抢单豆数量
     */
    private Integer getDialConfigLeftN(Long userId) {
        CoverGoodsUserBeansDTO coverGoodsUserBeansVO =
                coverGoodsBeansConfigUserService.selectTotalBeansLeftNumByUserId(userId);
        if (null != coverGoodsUserBeansVO) {
            return coverGoodsUserBeansVO.getTotalLeftNum() == null ? 0 : coverGoodsUserBeansVO.getTotalLeftNum();
        }
        return 0;
    }

    private Integer selectXTime() {
        Integer maxXTime = coverGoodsDialConfigMapper.selectMaxXTime();
        return maxXTime == null ? 0 : maxXTime;
    }

    /**
     * 用冒号(:)拼接 redis key
     *
     * @param strArray
     * @return
     */
    private String joinRedisKey(String... strArray) {
        StringBuilder builder = new StringBuilder();

        int i = 0;
        boolean endwithSplit = false;
        for (String onePart : strArray) {

            if (i > 0 && !endwithSplit) {
                builder.append(":");
            }

            builder.append(onePart);

            if (onePart.endsWith(":")) {
                endwithSplit = true;
            } else {
                endwithSplit = false;
            }

            i++;
        }

        return builder.toString();
    }

    @Data
    public static class UserTsDialInfoRedisBean implements Serializable {
        /**
         * 是否使用过免责卡
         */
        private Boolean useN;
        /**
         * 配置的y的时间, 单位秒
         */
        private Integer configYSeconds;
        /**
         * y的剩余时间, 单位秒
         */
        private Integer leftYSeconds;
        /**
         * 倒计时是否结束
         */
        private boolean countDownFinished;
    }
}

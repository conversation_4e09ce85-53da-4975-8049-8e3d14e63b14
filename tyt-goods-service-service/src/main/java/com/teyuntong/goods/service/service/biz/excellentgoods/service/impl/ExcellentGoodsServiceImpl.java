package com.teyuntong.goods.service.service.biz.excellentgoods.service.impl;

import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailCanUseCountVO;
import com.teyuntong.goods.service.client.excellentgoods.vo.TytExcellentGoodsCardUserDetailVO;
import com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.entity.TytExcellentGoodsCardUserDetail;
import com.teyuntong.goods.service.service.biz.excellentgoods.mybatis.mapper.TytExcellentGoodsCardUserDetailMapper;
import com.teyuntong.goods.service.service.biz.excellentgoods.service.ExcellentGoodsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class ExcellentGoodsServiceImpl implements ExcellentGoodsService {

    private final TytExcellentGoodsCardUserDetailMapper tytExcellentGoodsCardUserDetailMapper;

    @Override
    public List<TytExcellentGoodsCardUserDetailVO> getAllNoUseCarListByUserId(Long userId, Integer pageNum) {
        int pageSize = 20;
        List<TytExcellentGoodsCardUserDetailVO> result = new ArrayList<>();
        List<TytExcellentGoodsCardUserDetail> tytExcellentGoodsCardUserDetailList = tytExcellentGoodsCardUserDetailMapper.getAllNoUseCarListByUserId(userId, pageSize * (pageNum - 1), pageSize);
        for (TytExcellentGoodsCardUserDetail tytExcellentGoodsCardUserDetail : tytExcellentGoodsCardUserDetailList) {
            TytExcellentGoodsCardUserDetailVO tytExcellentGoodsCardUserDetailVO = new TytExcellentGoodsCardUserDetailVO();
            try {
                BeanUtils.copyProperties(tytExcellentGoodsCardUserDetailVO, tytExcellentGoodsCardUserDetail);
            } catch (Exception e) {
                continue;
            }
            tytExcellentGoodsCardUserDetailVO.setIsCanUse(false);
            if (tytExcellentGoodsCardUserDetailVO.getType() == 2
                    && (tytExcellentGoodsCardUserDetailVO.getValidDateBegin().compareTo(new Date()) <= 0)
                    && (tytExcellentGoodsCardUserDetailVO.getValidDateEnd().compareTo(new Date()) >= 0)) {
                tytExcellentGoodsCardUserDetailVO.setIsCanUse(true);
            }
            getMinRefreshInterval(tytExcellentGoodsCardUserDetailVO);
            result.add(tytExcellentGoodsCardUserDetailVO);
        }
        return result;
    }

    @Override
    public TytExcellentGoodsCardUserDetailCanUseCountVO getAllCanUseCarCountNumByUserId(Long userId) {
        TytExcellentGoodsCardUserDetailCanUseCountVO result = tytExcellentGoodsCardUserDetailMapper.getAllCanUseCarCountNumMax100AndLimitTimeByUserId(userId);
        if (result == null) {
            result = new TytExcellentGoodsCardUserDetailCanUseCountVO(0, null);
        }
        return result;
    }

    @Override
    public List<TytExcellentGoodsCardUserDetailVO> getAllCanUseCarListByUserId(Long userId, Integer pageNum) {
        int pageSize = 10;
        List<TytExcellentGoodsCardUserDetailVO> result = new ArrayList<>();
        List<TytExcellentGoodsCardUserDetail> detailList = tytExcellentGoodsCardUserDetailMapper.getAllCanUseCarListByUserIdPage(userId, pageSize * (pageNum - 1), pageSize);
        if (detailList == null) {
            result = new ArrayList<>();
        }
        for (TytExcellentGoodsCardUserDetail tytExcellentGoodsCardUserDetail : detailList) {
            TytExcellentGoodsCardUserDetailVO tytExcellentGoodsCardUserDetailVO = new TytExcellentGoodsCardUserDetailVO();
            try {
                BeanUtils.copyProperties(tytExcellentGoodsCardUserDetailVO, tytExcellentGoodsCardUserDetail);
            } catch (Exception e) {
                continue;
            }
            tytExcellentGoodsCardUserDetailVO.setIsCanUse(true);
            result.add(tytExcellentGoodsCardUserDetailVO);
        }
        return result;
    }

    private void getMinRefreshInterval(TytExcellentGoodsCardUserDetailVO tytExcellentGoodsCardUserDetailVO) {
        Integer minFirstRefreshInterval = tytExcellentGoodsCardUserDetailVO.getFirstRefreshInterval();
        Integer minSecondRefreshInterval = tytExcellentGoodsCardUserDetailVO.getSecondRefreshInterval();
        if(minFirstRefreshInterval!=null && minFirstRefreshInterval>0  && minSecondRefreshInterval!=null && minSecondRefreshInterval>0){
            tytExcellentGoodsCardUserDetailVO.setFirstRefreshInterval(Math.min(minFirstRefreshInterval, minSecondRefreshInterval));
        }
    }

}

package com.teyuntong.goods.service.service.biz.refresh.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源刷新配置表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-04-12
 */
@Getter
@Setter
@TableName("tyt_goods_refresh_config")
public class GoodsRefreshConfigDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分组类型code
     */
    private String code;

    /**
     * 分组类型名称
     */
    private String name;

    /**
     * 分组内容(json格式区分 {间隔:次数})
     */
    private String content;

    /**
     * 状态（1启用，0禁用）
     */
    private Integer status;

    /**
     * 状态（0未删除，1已删除）
     */
    private Integer del;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 创建人
     */
    private String createName;

    /**
     * 修改人
     */
    private String modifyName;

    /**
     * 配置类型，1:刷新配置  2:货源曝光限制
     */
    private Integer configType;

    /**
     * 货源类型，0:普通货源  1：优车货源
     */
    private Integer excellentGoods;

    /**
     * 货源价格，0:全部货源 1:一口价 2:有价电议 3:无价电议
     */
    private Integer goodsPriceType;

    /**
     * 发货时是否过好货模型  0：否  1：是
     */
    private Integer instantGrab;

    /**
     * 货主身份，格式：1-1，多个用逗号隔开
     */
    private String userGoodsType;

    /**
     * 生效维度，1:货主维度；2:货源维度(无需导入货主名单)
     */
    private Integer effectiveDimension;

    /**
     * 额外配置，JSON格式
     */
    private String extraConfig;
}

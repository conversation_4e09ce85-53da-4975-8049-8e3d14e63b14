package com.teyuntong.goods.service.service.biz.transport.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.client.transport.dto.TransportListDTO;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 运输信息表主表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-24
 */
@Mapper
public interface TransportMapper extends BaseMapper<TransportDO> {

    /**
     * 车签署协议更新货源相关字段数据
     *
     * @param transportMainDO 参数
     */
    void updateCarAgreementAboutTransportSomeFieldBySrcMsgId(TransportDO transportMainDO);

    /**
     * 我的发布中货源列表
     *
     * @param req
     * @param pageSize
     * @return
     */
    List<TransportDO> getPublishingList(@Param("req") TransportListDTO req, @Param("pageSize") Integer pageSize);

    /**
     * 获取用户发布中货源数量
     *
     * @param userId
     * @return
     */
    Integer getPublishingNum(@Param("userId") Long userId);

    /**
     * 获取最新一条记录
     *
     * @param srcMsgId
     * @return
     */
    TransportDO getLastBySrcMygId(@Param("srcMsgId") Long srcMsgId);

    /**
     * 获取相似货源
     *
     * @param similarityCode
     */
    int countSimilarityTransport(@Param("similarityCode") String similarityCode, @Param("srcMsgId") Long srcMsgId);

    /**
     * 获取相似货源有价个数
     *
     * @param similarityCode
     * @param startDate
     * @param endDate
     * @return
     */
    int getSimilarityTransportHavePriceCount(@Param("similarityCode") String similarityCode, @Param("startDate") Date startDate, @Param("endDate") Date endDate);

    int countSimilarityTransport(String similarityCode);

    /**
     * 获取货源最大重发次数
     */
    Integer getMaxResendCount(Long srcMsgId);

    /**
     * 返回最后一条货源
     *
     * @param srcMsgId
     * @return
     */
    TransportDO getLastBySrcMsgId(Long srcMsgId);

    /**
     * 返回货源可见的transportId
     *
     * @param srcMsgId
     * @return
     */
    List<Long> getValidTsIdBySrcMsgId(Long srcMsgId);

    /**
     * 将货源无效
     *
     * @param srcMsgId
     */
    void disableTransport(@Param("srcMsgId") Long srcMsgId);

    /**
     * 用户当天发布中货源数量
     *
     * @param userId
     * @return
     */
    int getUserDayPublishCount(@Param("userId") Long userId);

    /**
     * 修改货源信用曝光状态
     *
     * @param srcMsgId
     * @param creditRetop
     */
    void saveTransportCreditExposure(@Param("srcMsgId") Long srcMsgId, @Param("creditRetop") Integer creditRetop);

    /**
     * 不显示货源
     *
     * @param srcMsgId
     */
    void noDisplay(@Param("srcMsgId") Long srcMsgId);

    /**
     * 获取相似货源第一条
     *
     * @param similarityCode
     * @return
     */
    TransportDO getFirstBySimilarityCode(@Param("similarityCode") String similarityCode);


    List<Long> getTopNSrcMsgId(@Param("date") String date, @Param("userId") Long userId, @Param("topN") Integer topN);

    /**
     * 将货源设置为无效状态
     *
     * @param id
     */
    void invalidTransport(@Param("id") Long id);

    List<Long> getHistoryIdListBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId, @Param("startOfDay") Date startOfDay);

    int updateDisplayTypeBysrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("display") Integer display);

    void updateStatusByIds(@Param("idList") List<Long> todayIdList, @Param("status") Integer status, @Param("infoStatus") String infoStatus, @Param("display") int display);

    void updateLabelJsonBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("labelJson") String labelJson);

    List<Long> getTodayIdListBySrcMsgId(@Param("srcMsgId") Long srcMsgId, @Param("userId") Long userId, @Param("status") Integer status);

    /**
     * 更新货源
     *
     * @param transport
     * @return
     */
    int updateTransport(@Param("transport") TransportDO transport);
}

package com.teyuntong.goods.service.service.biz.refresh.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 货源刷新手动曝光用户提交日志表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-23
 */
@Getter
@Setter
@TableName("tyt_goods_refresh_manual_log")
public class GoodsRefreshManualLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 货源id
     */
    private Long srcMsgId;

    /**
     * 发货人id
     */
    private Long userId;

    /**
     * 提交任务类型，1填运费，2转一口价，4补充目的地
     */
    private Integer itemValue;

    /**
     * 提交前运费
     */
    private String priceBefore;

    /**
     * 提交后运费
     */
    private String priceAfter;

    /**
     * 提交前货源类型（电议1，一口价2）
     */
    private Integer publishTypeBefore;

    /**
     * 提交后货源类型（电议1，一口价2）
     */
    private Integer publishTypeAfter;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建时间
     */
    private Date modifyTime;

    /**
     * 系统推荐金额
     */
    private String priceSuggest;

    /**
     * 来源页面：0未知；1货源详情页货源诊断入口；2发货成功货源诊断入口；3发货页低速找车弹窗；4PC发货前填价弹窗；
     */
    private Integer sourcePage;
}

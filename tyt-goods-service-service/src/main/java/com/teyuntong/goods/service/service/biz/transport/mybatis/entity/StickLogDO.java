package com.teyuntong.goods.service.service.biz.transport.mybatis.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 置顶记录表
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-02-16
 */
@Getter
@Setter
@TableName("tyt_stick_log")
public class StickLogDO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运行批次号 格式：yyyymmddhhmm
     */
    private String batchNo;

    /**
     * 原货物信息编号
     */
    private Long sourceTsId;

    /**
     * 原货物信息的发布时间
     */
    private Date sourceTsCtime;

    /**
     * 发布的新货物编号
     */
    private Long tsId;

    /**
     * 货物信息浏览次数
     */
    private Integer tsBrowseNum;

    /**
     * 状态 0：创建； 1：置顶成功；2：置顶失败；
     */
    private String status;

    /**
     * 信息创建时间
     */
    private Date ctime;

    /**
     * 修改时间
     */
    private Date mtime;

    /**
     * 置顶规则ID
     */
    private Long ruleId;

    /**
     * 置顶数
     */
    private Integer topNum;

    /**
     * 信息置顶时间
     */
    private Date stickTime;

    /**
     * 刷新类型, 空是旧刷新和首页刷新, 1->货源一口价 2->货源有价电议 3->货源无价电议论 11 用户信用刷新
     */
    private Integer refreshType;
}

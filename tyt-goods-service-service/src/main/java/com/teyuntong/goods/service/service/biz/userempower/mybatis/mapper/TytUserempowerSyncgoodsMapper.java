package com.teyuntong.goods.service.service.biz.userempower.mybatis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.teyuntong.goods.service.service.biz.userempower.mybatis.entity.TytUserempowerSyncgoodsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 货源授权弹窗数据表 Mapper 接口
 * </p>
 *
 * <AUTHOR> 自动生成
 * @since 2025-05-15
 */
@Mapper
public interface TytUserempowerSyncgoodsMapper extends BaseMapper<TytUserempowerSyncgoodsDO> {

    /**
     * 根据用户ID查询未删除的授权记录
     *
     * @param userId 用户ID
     * @return 授权记录
     */
    TytUserempowerSyncgoodsDO selectByUserId(@Param("userId") Long userId);

    /**
     * 更新用户授权状态为已授权
     *
     * @param userId 用户ID
     * @return 影响行数
     */
    int updateAuthStatus(@Param("userId") Long userId);

    /**
     * 插入用户授权记录并设置为已授权
     *
     * @param userId 用户ID
     * @param goodsUserName 货方姓名
     * @return 影响行数
     */
    int insertAuthRecord(@Param("userId") Long userId,
                         @Param("goodsUserName") String goodsUserName);
}

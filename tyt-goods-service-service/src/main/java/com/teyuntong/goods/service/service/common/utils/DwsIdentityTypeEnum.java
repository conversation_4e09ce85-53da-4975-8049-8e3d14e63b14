package com.teyuntong.goods.service.service.common.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 经分口径的身份类型
 *
 * <AUTHOR>
 * @since 2024/05/31 10:15
 */
@Getter
@AllArgsConstructor
public enum DwsIdentityTypeEnum {

    ALL_USER(0, "全部"),
    LOGISTICS_COMPANIES(1, "物流公司"),
    CARGO_STATION(2, "货站"),
    BUSINESS_CARGO_OWNER(3, "企业货主"),
    CARGO_OWNER(4, "个人货主"),
    ;

    private final Integer code;
    private final String name;

    /**
     * 是否是一手货站、企业货主
     */
    public static boolean isCargoOwner(Integer code) {
        return BUSINESS_CARGO_OWNER.code.equals(code) || CARGO_OWNER.code.equals(code);
    }

    /**
     * 返回名称
     */
    public static String getName(Integer code) {
        for (DwsIdentityTypeEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.getName();
            }
        }
        return "";
    }

}

package com.teyuntong.goods.service.service.biz.transport.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.teyuntong.goods.service.service.biz.transport.mybatis.entity.TransportExtendDO;

/**
 * 客户资料服务类
 *
 * <AUTHOR>
 * @since 2024-01-18 17:34
 */
public interface TransportExtendService extends IService<TransportExtendDO> {


    TransportExtendDO getByTsId(Long tsId);

    /**
     * 判断是否是秒抢货源，秒抢货源新增对照组，实验组70%，走原来的逻辑；对照组30%，只打标走普通货源逻辑
     *
     * @return 1秒抢实验组；2秒抢对照组；0非秒抢
     */
    Integer judgeSeckillGoods(boolean isSeckillGoods, Long srcMsgId);

    /**
     * 更新运输扩展信息
     *
     * @param transportExtend
     */
    int updateTransportExtend(TransportExtendDO transportExtend);
}

package com.teyuntong.goods.service.client.transport.vo;

import lombok.Getter;
import lombok.Setter;

/**
 * 发布时的弹窗信息，如曝光成功、失败弹窗
 *
 * <AUTHOR>
 * @since 2025/04/17 17:38
 */
@Getter
@Setter
public class PublishNoticeDataVO {

    /**
     * 弹窗类型，不同接口返回不同类型。
     * 货源曝光弹窗接口：1：曝光成功&命中规则，2：曝光成功&未命中规则，3：曝光卡已用尽&命中规则，4：曝光卡已用尽&未命中规则，5：曝光卡已用尽，但有曝光卡待领取
     */
    private Integer type;

    /**
     * 弹窗标题
     */
    private String title;

    /**
     * 弹窗内容
     */
    private String content;

    /**
     * 左边内容
     */
    private String leftContent;

    /**
     * 右边内容
     */
    private String rightContent;

    /**
     * 按钮内容
     */
    private String btnText;

    /**
     * 按钮链接
     */
    private String btnLink;
}

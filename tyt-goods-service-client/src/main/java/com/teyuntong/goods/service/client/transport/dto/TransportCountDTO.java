package com.teyuntong.goods.service.client.transport.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 货源信息
 *
 * @since 2024-01-18
 */
@Data
public class TransportCountDTO {

    /**
     * 用户id
     */
    @NotEmpty(message = "用户id不能为空")
    private List<Long> userIdList;

    /**
     * 状态 1有效（发布中），0无效（已过期），2待定（QQ专用），3阻止（QQ专用），4成交，5取消状态
     */
    private Integer status;

    /**
     * 发布时间时间
     */
    @NotNull(message = "开始时间不能为空")
    private Date startTime;
    /**
     * 发布时间
     */
    private Date endTime;

    /**
     * 货源类型（电议1，一口价2）
     */
    private Integer publishType;

    /**
     * 订金是否退还（0不退还；1退还）
     */
    private Integer refundFlag;
    /**
     * 是否是优车货源（0:普通货源 1：优车货源 2：专车货源）
     */
    private Integer excellentGoods;


}
